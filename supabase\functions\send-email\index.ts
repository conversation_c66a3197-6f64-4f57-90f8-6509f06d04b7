import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface EmailRequest {
  to: string
  subject: string
  message?: string
  htmlContent?: string
  textContent?: string
  config: {
    smtpHost: string
    smtpPort: string
    smtpUser: string
    smtpPassword: string
    fromEmail: string
    fromName: string
  }
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { to, subject, message, htmlContent, textContent, config }: EmailRequest = await req.json()

    // Validate required fields
    if (!to || !subject || (!message && !htmlContent) || !config.smtpPassword) {
      throw new Error('Missing required email parameters')
    }

    // Use provided content or fallback to message
    const finalHtmlContent = htmlContent || (message ? message.replace(/\n/g, '<br>') : '')
    const finalTextContent = textContent || message || ''
    const fromEmail = '<EMAIL>'
    const fromName = config.fromName || 'Hệ thống quản lý thuê bao'

    // Try Resend API first (recommended)
    if (config.smtpPassword.startsWith('re_')) {
      try {
        const resendResponse = await fetch('https://api.resend.com/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.smtpPassword}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            from: `${fromName} <${fromEmail}>`,
            to: [to],
            subject: subject,
            html: finalHtmlContent,
            text: finalTextContent
          })
        })

        if (resendResponse.ok) {
          const result = await resendResponse.json()
          return new Response(
            JSON.stringify({ 
              success: true, 
              message: 'Email sent successfully via Resend',
              id: result.id 
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        } else {
          const errorData = await resendResponse.json()
          throw new Error(`Resend API error: ${errorData.message || 'Unknown error'}`)
        }
      } catch (error) {
        console.log('Resend failed:', error)
        throw error // Re-throw Resend errors since it's our primary service
      }
    }

    // Try SendGrid API (if password starts with SG.)
    if (config.smtpPassword.startsWith('SG.')) {
      try {
        const emailBody = {
          personalizations: [{
            to: [{ email: to }],
            subject: subject
          }],
          from: {
            email: fromEmail,
            name: fromName
          },
          content: [{
            type: 'text/html',
            value: finalHtmlContent
          }]
        }

        const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${config.smtpPassword}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(emailBody)
        })

        if (response.ok) {
          return new Response(
            JSON.stringify({ success: true, message: 'Email sent successfully via SendGrid' }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        } else {
          const errorData = await response.text()
          throw new Error(`SendGrid API error: ${errorData}`)
        }
      } catch (error) {
        console.log('SendGrid failed:', error)
        throw error
      }
    }

    // Try Mailgun API (if host contains mailgun)
    if (config.smtpHost && config.smtpHost.includes('mailgun')) {
      try {
        const domain = config.smtpHost.replace('smtp.', '').replace('.mailgun.org', '')
        const mailgunUrl = `https://api.mailgun.net/v3/${domain}/messages`
        
        const formData = new FormData()
        formData.append('from', `${fromName} <${fromEmail}>`)
        formData.append('to', to)
        formData.append('subject', subject)
        formData.append('html', finalHtmlContent)
        formData.append('text', finalTextContent)

        const response = await fetch(mailgunUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa(`api:${config.smtpPassword}`)}`
          },
          body: formData
        })

        if (response.ok) {
          return new Response(
            JSON.stringify({ success: true, message: 'Email sent successfully via Mailgun' }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        } else {
          const errorData = await response.text()
          throw new Error(`Mailgun API error: ${errorData}`)
        }
      } catch (error) {
        console.log('Mailgun failed:', error)
        throw error
      }
    }

    // Try Brevo (formerly Sendinblue) API
    try {
      const brevoResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
        method: 'POST',
        headers: {
          'api-key': config.smtpPassword,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sender: {
            name: fromName,
            email: fromEmail
          },
          to: [{ email: to }],
          subject: subject,
          htmlContent: finalHtmlContent,
          textContent: finalTextContent
        })
      })

      if (brevoResponse.ok) {
        return new Response(
          JSON.stringify({ success: true, message: 'Email sent successfully via Brevo' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }
    } catch (error) {
      console.log('Brevo failed:', error)
    }

    // If no specific service is detected or all fail, provide helpful error message
    throw new Error(`Email sending failed. Please use one of these recommended services:

🚀 **RESEND (Recommended - Modern & Reliable):**
1. Sign up at https://resend.com
2. Verify your domain or use their test domain
3. Create an API key (starts with 're_')
4. Use the API key as your "Mật khẩu ứng dụng"
5. Set "Email người gửi" to your verified email
6. Host and Port can be left empty

📧 **SENDGRID (Alternative):**
1. Sign up at https://sendgrid.com
2. Create an API key (starts with 'SG.')
3. Use the API key as your "Mật khẩu ứng dụng"

⚙️ **MAILGUN (For Advanced Users):**
1. Set up domain at https://mailgun.com
2. Use domain SMTP credentials
3. Set SMTP host to your domain's mailgun host

Current config: API Key starts with '${config.smtpPassword.substring(0, 3)}...'`)

  } catch (error) {
    console.error('Email sending error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message,
        details: 'Please check your email configuration and try again'
      }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})