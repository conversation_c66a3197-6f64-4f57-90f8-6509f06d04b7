import { differenceInDays } from 'date-fns'

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('vi-VN')
}

export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800'
    case 'expired': return 'bg-red-100 text-red-800'
    case 'cancelled': return 'bg-gray-100 text-gray-800'
    default: return 'bg-blue-100 text-blue-800'
  }
}

export const getStatusText = (status: string): string => {
  switch (status) {
    case 'active': return 'Hoạt động'
    case 'expired': return 'Hết hạn'
    case 'cancelled': return 'Đã hủy'
    default: return status
  }
}

export const getDaysUntilExpiry = (expiryDate: string): number => {
  return differenceInDays(new Date(expiryDate), new Date())
}

export const isExpiringSoon = (expiryDate: string, reminderDays: number = 3): boolean => {
  const days = getDaysUntilExpiry(expiryDate)
  return days <= reminderDays && days > 0
}