import React, { useState, useEffect } from 'react'
import { settingsService, type Setting } from '../lib/database'
import { notificationService } from '../lib/notifications'
import { Settings as SettingsIcon, Mail, MessageSquare, Database, Shield } from 'lucide-react'

export function Settings() {
  const [loading, setLoading] = useState(true)
  const [emailSettings, setEmailSettings] = useState({
    smtpHost: '',
    smtpPort: '',
    smtpUser: '',
    smtpPassword: '',
    fromEmail: '',
    fromName: ''
  })

  const [smsSettings, setSmsSettings] = useState({
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: ''
  })

  const [notificationSettings, setNotificationSettings] = useState({
    emailEnabled: true,
    smsEnabled: false,
    reminderDays: 3,
    autoReminder: true
  })

  const [testEmail, setTestEmail] = useState('')
  const [testPhone, setTestPhone] = useState('')
  const [testMessage, setTestMessage] = useState('<PERSON><PERSON><PERSON> là tin nhắn thử nghiệm từ hệ thống quản lý thuê bao.')
  const [testSubject, setTestSubject] = useState('Thử nghiệm gửi email')
  const [testLoading, setTestLoading] = useState(false)
  const [templateTestLoading, setTemplateTestLoading] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState('welcome')
  const [templateTestEmail, setTemplateTestEmail] = useState('')

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      const [emailConfig, smsConfig, notificationConfig] = await Promise.all([
        settingsService.get('email_config'),
        settingsService.get('sms_config'),
        settingsService.get('notification_config')
      ])

      if (emailConfig) setEmailSettings(emailConfig)
      if (smsConfig) setSmsSettings(smsConfig)
      if (notificationConfig) setNotificationSettings(notificationConfig)
    } catch (error) {
      console.error('Error loading settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveEmailSettings = async () => {
    try {
      await settingsService.set('email_config', emailSettings)
      alert('Cài đặt email đã được lưu!')
    } catch (error) {
      console.error('Error saving email settings:', error)
      alert('Lỗi khi lưu cài đặt email')
    }
  }

  const handleSaveSmsSettings = async () => {
    try {
      await settingsService.set('sms_config', smsSettings)
      alert('Cài đặt SMS đã được lưu!')
    } catch (error) {
      console.error('Error saving SMS settings:', error)
      alert('Lỗi khi lưu cài đặt SMS')
    }
  }

  const handleSaveNotificationSettings = async () => {
    try {
      await settingsService.set('notification_config', notificationSettings)
      alert('Cài đặt thông báo đã được lưu!')
    } catch (error) {
      console.error('Error saving notification settings:', error)
      alert('Lỗi khi lưu cài đặt thông báo')
    }
  }

  const handleTestEmail = async () => {
    if (!testEmail) {
      alert('Vui lòng nhập địa chỉ email')
      return
    }

    setTestLoading(true)
    try {
      const result = await notificationService.sendTestEmail(testEmail, testSubject, testMessage)
      alert(result.message)
    } catch (error) {
      alert('Lỗi khi gửi email thử nghiệm')
    } finally {
      setTestLoading(false)
    }
  }

  const handleTestSms = async () => {
    if (!testPhone) {
      alert('Vui lòng nhập số điện thoại')
      return
    }

    setTestLoading(true)
    try {
      const result = await notificationService.sendTestSms(testPhone, testMessage)
      alert(result.message)
    } catch (error) {
      alert('Lỗi khi gửi SMS thử nghiệm')
    } finally {
      setTestLoading(false)
    }
  }

  const handleTestEmailTemplate = async () => {
    if (!templateTestEmail) {
      alert('Vui lòng nhập địa chỉ email')
      return
    }

    setTemplateTestLoading(true)
    try {
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/test-email-template`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: templateTestEmail,
          template: selectedTemplate
        })
      })

      const result = await response.json()
      
      if (result.success) {
        alert(`Email template "${selectedTemplate}" đã được gửi thành công!`)
      } else {
        alert(`Lỗi: ${result.message}`)
      }
    } catch (error) {
      alert('Lỗi khi gửi email template')
    } finally {
      setTemplateTestLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center gap-2 mb-6">
        <SettingsIcon className="h-6 w-6" />
        <h2 className="text-2xl font-bold text-gray-900">Cài đặt hệ thống</h2>
      </div>

      <div className="space-y-6">
        {/* Email Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <Mail className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Cài đặt Email</h3>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SMTP Host
              </label>
              <input
                type="text"
                value={emailSettings.smtpHost}
                onChange={(e) => setEmailSettings({...emailSettings, smtpHost: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="smtp.gmail.com"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SMTP Port
              </label>
              <input
                type="text"
                value={emailSettings.smtpPort}
                onChange={(e) => setEmailSettings({...emailSettings, smtpPort: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="587"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email người gửi
              </label>
              <input
                type="email"
                value={emailSettings.smtpUser}
                onChange={(e) => setEmailSettings({...emailSettings, smtpUser: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mật khẩu ứng dụng
              </label>
              <input
                type="password"
                value={emailSettings.smtpPassword}
                onChange={(e) => setEmailSettings({...emailSettings, smtpPassword: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="App password"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tên người gửi
              </label>
              <input
                type="text"
                value={emailSettings.fromName}
                onChange={(e) => setEmailSettings({...emailSettings, fromName: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Hệ thống quản lý thuê bao"
              />
            </div>
          </div>
          
          <button
            onClick={handleSaveEmailSettings}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Lưu cài đặt Email
          </button>
          
          {/* Email Test Form */}
          <div className="mt-6 p-4 bg-blue-50 rounded-md">
            <h4 className="text-md font-semibold mb-3">Thử nghiệm gửi Email</h4>
            <div className="mb-3 p-3 bg-blue-100 rounded text-sm">
              <p><strong>📧 Template email có sẵn:</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Nhắc nhở subscription sắp hết hạn</li>
                <li>Thông báo subscription đã hết hạn</li>
                <li>Cảnh báo quá hạn và chuẩn bị xóa tài khoản</li>
                <li>Thông báo gia hạn thành công</li>
                <li>Thông báo thay đổi phí dịch vụ</li>
              </ul>
            </div>
            <div className="mb-3 p-3 bg-blue-100 rounded text-sm">
              <p><strong>🚀 Khuyến nghị: Sử dụng Resend (Hiện đại & Đáng tin cậy):</strong></p>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Đăng ký tại <a href="https://resend.com" target="_blank" className="text-blue-600 underline">resend.com</a></li>
                <li>Xác thực domain của bạn hoặc sử dụng test domain</li>
                <li>Tạo API Key (bắt đầu bằng "re_")</li>
                <li>Sử dụng API Key làm "Mật khẩu ứng dụng"</li>
                <li>Đặt "Email người gửi" là email đã xác thực</li>
                <li>Host và Port có thể để trống</li>
              </ol>
            </div>
            <div className="mb-3 p-3 bg-green-100 rounded text-sm">
              <p><strong>📧 Lựa chọn khác: SendGrid (100 email/ngày miễn phí):</strong></p>
              <ol className="list-decimal list-inside mt-2 space-y-1">
                <li>Đăng ký tại <a href="https://sendgrid.com" target="_blank" className="text-blue-600 underline">sendgrid.com</a></li>
                <li>Tạo API Key (bắt đầu bằng "SG.")</li>
                <li>Sử dụng API Key làm "Mật khẩu ứng dụng"</li>
              </ol>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email nhận
                </label>
                <input
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tiêu đề
                </label>
                <input
                  type="text"
                  value={testSubject}
                  onChange={(e) => setTestSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nội dung
                </label>
                <textarea
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <button
                onClick={handleTestEmail}
                disabled={testLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {testLoading ? 'Đang gửi...' : 'Gửi Email thử nghiệm'}
              </button>
            </div>
          </div>
        </div>

        {/* Email Template Testing */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <Mail className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold">Thử nghiệm Email Templates</h3>
          </div>
          
          <div className="mb-4 p-4 bg-indigo-50 rounded-md">
            <h4 className="text-md font-semibold mb-3">📧 Các template email có sẵn:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="p-3 bg-white rounded border">
                <strong>🎉 Welcome:</strong> Email chào mừng khách hàng mới
              </div>
              <div className="p-3 bg-white rounded border">
                <strong>🔔 Expiration Reminder:</strong> Nhắc nhở sắp hết hạn
              </div>
              <div className="p-3 bg-white rounded border">
                <strong>❌ Expired:</strong> Thông báo đã hết hạn
              </div>
              <div className="p-3 bg-white rounded border">
                <strong>🚨 Overdue Warning:</strong> Cảnh báo quá hạn
              </div>
              <div className="p-3 bg-white rounded border">
                <strong>✅ Renewal Success:</strong> Thông báo gia hạn thành công
              </div>
              <div className="p-3 bg-white rounded border">
                <strong>📢 Price Change:</strong> Thông báo thay đổi giá
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Chọn template
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="welcome">🎉 Welcome - Chào mừng khách hàng mới</option>
                  <option value="expirationReminder">🔔 Expiration Reminder - Nhắc nhở sắp hết hạn</option>
                  <option value="expired">❌ Expired - Thông báo đã hết hạn</option>
                  <option value="overdueWarning">🚨 Overdue Warning - Cảnh báo quá hạn</option>
                  <option value="renewalSuccess">✅ Renewal Success - Gia hạn thành công</option>
                  <option value="priceChange">📢 Price Change - Thay đổi giá</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email nhận
                </label>
                <input
                  type="email"
                  value={templateTestEmail}
                  onChange={(e) => setTemplateTestEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Lưu ý:</strong> Email sẽ được gửi với dữ liệu mẫu để bạn có thể xem trước giao diện và nội dung của từng template.
              </p>
            </div>
            
            <button
              onClick={handleTestEmailTemplate}
              disabled={templateTestLoading}
              className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {templateTestLoading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
              {templateTestLoading ? 'Đang gửi...' : 'Gửi Email Template'}
            </button>
          </div>
        </div>

        {/* SMS Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <MessageSquare className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold">Cài đặt SMS (Twilio)</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account SID
              </label>
              <input
                type="text"
                value={smsSettings.twilioAccountSid}
                onChange={(e) => setSmsSettings({...smsSettings, twilioAccountSid: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="AC..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Auth Token
              </label>
              <input
                type="password"
                value={smsSettings.twilioAuthToken}
                onChange={(e) => setSmsSettings({...smsSettings, twilioAuthToken: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Auth Token"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Số điện thoại Twilio
              </label>
              <input
                type="text"
                value={smsSettings.twilioPhoneNumber}
                onChange={(e) => setSmsSettings({...smsSettings, twilioPhoneNumber: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="+1234567890"
              />
            </div>
          </div>
          
          <button
            onClick={handleSaveSmsSettings}
            className="mt-4 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Lưu cài đặt SMS
          </button>
          
          {/* SMS Test Form */}
          <div className="mt-6 p-4 bg-green-50 rounded-md">
            <h4 className="text-md font-semibold mb-3">Thử nghiệm gửi SMS</h4>
            <div className="mb-3 p-3 bg-green-100 rounded text-sm">
              <p><strong>📱 Mẫu SMS có sẵn (Tiếng Việt):</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li><strong>Nhắc nhở:</strong> "🔔 Xin chào [Tên]! Dịch vụ [Sản phẩm] sẽ hết hạn vào [Ngày]..."</li>
                <li><strong>Hết hạn:</strong> "❌ Thông báo: Dịch vụ [Sản phẩm] đã hết hạn..."</li>
                <li><strong>Chào mừng:</strong> "🎉 Chào mừng [Tên]! Đăng ký [Sản phẩm] thành công..."</li>
                <li><strong>Gia hạn:</strong> "✅ [Tên] ơi! Gia hạn [Sản phẩm] thành công..."</li>
                <li><strong>Cảnh báo:</strong> "🚨 CẢNH BÁO: [Tên], dịch vụ đã quá hạn..."</li>
              </ul>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              <strong>Lưu ý:</strong> Số điện thoại phải có mã quốc gia (VD: +84901234567)
            </p>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Số điện thoại
                </label>
                <input
                  type="tel"
                  value={testPhone}
                  onChange={(e) => setTestPhone(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="+84901234567 (bắt buộc có mã quốc gia)"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nội dung SMS
                </label>
                <textarea
                  value={testMessage}
                  onChange={(e) => setTestMessage(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>
              <button
                onClick={handleTestSms}
                disabled={testLoading}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {testLoading ? 'Đang gửi...' : 'Gửi SMS thử nghiệm'}
              </button>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <Shield className="h-5 w-5 text-purple-600" />
            <h3 className="text-lg font-semibold">Cài đặt thông báo</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="emailEnabled"
                checked={notificationSettings.emailEnabled}
                onChange={(e) => setNotificationSettings({...notificationSettings, emailEnabled: e.target.checked})}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="emailEnabled" className="text-sm font-medium text-gray-700">
                Bật thông báo qua Email
              </label>
            </div>
            
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="smsEnabled"
                checked={notificationSettings.smsEnabled}
                onChange={(e) => setNotificationSettings({...notificationSettings, smsEnabled: e.target.checked})}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="smsEnabled" className="text-sm font-medium text-gray-700">
                Bật thông báo qua SMS
              </label>
            </div>
            
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="autoReminder"
                checked={notificationSettings.autoReminder}
                onChange={(e) => setNotificationSettings({...notificationSettings, autoReminder: e.target.checked})}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="autoReminder" className="text-sm font-medium text-gray-700">
                Bật nhắc nhở tự động
              </label>
            </div>
            
            <div className="flex items-center gap-3">
              <label className="text-sm font-medium text-gray-700">
                Nhắc nhở trước khi hết hạn:
              </label>
              <input
                type="number"
                min="1"
                max="30"
                value={notificationSettings.reminderDays}
                onChange={(e) => setNotificationSettings({...notificationSettings, reminderDays: parseInt(e.target.value)})}
                className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">ngày</span>
            </div>
          </div>
          
          <button
            onClick={handleSaveNotificationSettings}
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            Lưu cài đặt thông báo
          </button>
        </div>

        {/* Database Info */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center gap-2 mb-4">
            <Database className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold">Thông tin cơ sở dữ liệu</h3>
          </div>
          
          <div className="bg-gray-50 rounded-md p-4">
            <p className="text-sm text-gray-600 mb-2">
              <strong>Lưu ý:</strong> Để sử dụng hệ thống, bạn cần thiết lập cơ sở dữ liệu Supabase với các bảng sau:
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• <code>customers</code> - Thông tin khách hàng</li>
              <li>• <code>products</code> - Danh sách sản phẩm/dịch vụ</li>
              <li>• <code>subscriptions</code> - Giao dịch thuê bao</li>
              <li>• <code>price_history</code> - Lịch sử giá sản phẩm</li>
            </ul>
            <p className="text-sm text-gray-600 mt-2">
              Hệ thống cũng cần bucket <code>transaction-images</code> để lưu trữ hình ảnh giao dịch.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}