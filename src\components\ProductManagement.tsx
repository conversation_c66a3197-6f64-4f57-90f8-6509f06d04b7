import React, { useState, useEffect } from 'react'
import { productService, type Product, type PriceHistory } from '../lib/database'
import { formatCurrency } from '../lib/utils'
import { Plus, Edit, Trash2, Clock, DollarSign, History } from 'lucide-react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'

const productSchema = yup.object({
  name: yup.string().required('Tên sản phẩm là bắt buộc'),
  service_type: yup.string().required('Loại dịch vụ là bắt buộc'),
  duration_months: yup.number().required('Thời hạn là bắt buộc').positive('Thời hạn phải > 0'),
  current_price: yup.number().required('Giá là bắt buộc').positive('<PERSON><PERSON><PERSON> phải > 0'),
  description: yup.string().nullable()
})

type ProductFormData = yup.InferType<typeof productSchema>

export function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [priceHistory, setPriceHistory] = useState<PriceHistory[]>([])
  const [showPriceHistory, setShowPriceHistory] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)

  const { register, handleSubmit, reset, formState: { errors } } = useForm<ProductFormData>({
    resolver: yupResolver(productSchema)
  })

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      const data = await productService.getAll()
      setProducts(data)
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchPriceHistory = async (productId: string) => {
    try {
      const data = await productService.getPriceHistory(productId)
      setPriceHistory(data)
    } catch (error) {
      console.error('Error fetching price history:', error)
    }
  }

  const onSubmit = async (data: ProductFormData) => {
    try {
      if (editingProduct) {
        await productService.update(editingProduct.id, data, editingProduct.current_price)
      } else {
        await productService.create(data)
      }

      await fetchProducts()
      setIsModalOpen(false)
      setEditingProduct(null)
      reset()
    } catch (error) {
      console.error('Error saving product:', error)
    }
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    reset({
      name: product.name,
      service_type: product.service_type,
      duration_months: product.duration_months,
      current_price: product.current_price,
      description: product.description
    })
    setIsModalOpen(true)
  }

  const handleDelete = async (productId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
      try {
        await productService.delete(productId)
        await fetchProducts()
      } catch (error) {
        console.error('Error deleting product:', error)
      }
    }
  }

  const handleShowPriceHistory = (product: Product) => {
    setSelectedProduct(product)
    fetchPriceHistory(product.id)
    setShowPriceHistory(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Quản lý sản phẩm</h2>
        <button
          onClick={() => {
            setEditingProduct(null)
            reset()
            setIsModalOpen(true)
          }}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-5 w-5" />
          Thêm sản phẩm
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product) => (
          <div key={product.id} className="bg-white rounded-lg shadow-sm border p-4 md:p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-900">{product.name}</h3>
                <p className="text-sm text-gray-600">{product.service_type}</p>
              </div>
              <div className="flex space-x-1 md:space-x-2">
                <button
                  onClick={() => handleShowPriceHistory(product)}
                  className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                >
                  <History className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEdit(product)}
                  className="p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(product.id)}
                  className="p-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Clock className="h-4 w-4" />
                <span>{product.duration_months} tháng</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <DollarSign className="h-4 w-4" />
                <span className="font-medium">{formatCurrency(product.current_price)}</span>
              </div>
            </div>

            {product.description && (
              <p className="text-sm text-gray-600 mt-3">{product.description}</p>
            )}
          </div>
        ))}
      </div>

      {products.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">Chưa có sản phẩm nào</p>
        </div>
      )}

      {/* Product Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              {editingProduct ? 'Chỉnh sửa sản phẩm' : 'Thêm sản phẩm mới'}
            </h3>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tên sản phẩm *
                  </label>
                  <input
                    {...register('name')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="VD: YouTube Premium"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Loại dịch vụ *
                  </label>
                  <select
                    {...register('service_type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn loại dịch vụ</option>
                    <option value="Video Streaming">Video Streaming</option>
                    <option value="Music Streaming">Music Streaming</option>
                    <option value="Cloud Storage">Cloud Storage</option>
                    <option value="Office Suite">Office Suite</option>
                    <option value="Gaming">Gaming</option>
                    <option value="VPN">VPN</option>
                    <option value="Other">Khác</option>
                  </select>
                  {errors.service_type && (
                    <p className="text-red-500 text-sm mt-1">{errors.service_type.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Thời hạn (tháng) *
                  </label>
                  <input
                    {...register('duration_months')}
                    type="number"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.duration_months && (
                    <p className="text-red-500 text-sm mt-1">{errors.duration_months.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Giá hiện tại (VND) *
                  </label>
                  <input
                    {...register('current_price')}
                    type="number"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.current_price && (
                    <p className="text-red-500 text-sm mt-1">{errors.current_price.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Mô tả
                  </label>
                  <textarea
                    {...register('description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mô tả về sản phẩm..."
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {editingProduct ? 'Cập nhật' : 'Thêm'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Price History Modal */}
      {showPriceHistory && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
            <h3 className="text-lg font-semibold mb-4">
              Lịch sử giá - {selectedProduct.name}
            </h3>
            <div className="max-h-96 overflow-y-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Giá
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ngày hiệu lực
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {priceHistory.map((history) => (
                    <tr key={history.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {formatCurrency(history.price)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(history.effective_date).toLocaleDateString('vi-VN')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="flex justify-end mt-4">
              <button
                onClick={() => setShowPriceHistory(false)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}