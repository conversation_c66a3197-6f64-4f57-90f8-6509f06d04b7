export interface EmailTemplateData {
  customerName: string
  productName: string
  expiryDate: string
  startDate?: string
  price?: string
  daysOverdue?: number
  newPrice?: string
  oldPrice?: string
  effectiveDate?: string
  contactInfo?: {
    phone: string
    email: string
    website?: string
    address: string
  }
}

export const emailTemplates = {
  // Template nhắc nhở subscription sắp hết hạn
  expirationReminder: (data: EmailTemplateData) => ({
    subject: `🔔 Nhắc nhở: Dịch vụ ${data.productName} sắp hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhắc nhở gia hạn dịch vụ</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .alert-box { background-color: #fef3cd; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .alert-icon { font-size: 24px; margin-bottom: 10px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { margin: 20px 0; padding: 15px; background-color: #e3f2fd; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Thông báo quan trọng</h1>
            <p>Dịch vụ của bạn sắp hết hạn</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="alert-box">
                <div class="alert-icon">⚠️</div>
                <h3>Dịch vụ sắp hết hạn!</h3>
                <p>Chúng tôi muốn nhắc nhở bạn rằng dịch vụ của bạn sẽ hết hạn trong thời gian tới.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                ${data.price ? `<p><strong>Giá gia hạn:</strong> ${data.price}</p>` : ''}
            </div>
            
            <p>Để tiếp tục sử dụng dịch vụ mà không bị gián đoạn, vui lòng liên hệ với chúng tôi để gia hạn trước ngày hết hạn.</p>
            
            <div style="text-align: center;">
                <a href="#" class="cta-button">💬 Liên hệ gia hạn ngay</a>
            </div>
            
            ${data.contactInfo ? `
            <div class="contact-info">
                <h4>📞 Thông tin liên hệ</h4>
                <p><strong>Điện thoại:</strong> ${data.contactInfo.phone}</p>
                <p><strong>Email:</strong> ${data.contactInfo.email}</p>
                ${data.contactInfo.address ? `<p><strong>Địa chỉ:</strong> ${data.contactInfo.address}</p>` : ''}
            </div>
            ` : ''}
            
            <p>Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br>
            <strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Vui lòng không trả lời trực tiếp email này.</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
Xin chào ${data.customerName},

Chúng tôi muốn nhắc nhở bạn rằng dịch vụ ${data.productName} của bạn sẽ hết hạn vào ngày ${data.expiryDate}.

Để tiếp tục sử dụng dịch vụ mà không bị gián đoạn, vui lòng liên hệ với chúng tôi để gia hạn trước ngày hết hạn.

${data.contactInfo ? `
Thông tin liên hệ:
- Điện thoại: ${data.contactInfo.phone}
- Email: ${data.contactInfo.email}
- Địa chỉ: ${data.contactInfo.address}
` : ''}

Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  // Template thông báo subscription đã hết hạn
  expired: (data: EmailTemplateData) => ({
    subject: `❌ Thông báo: Dịch vụ ${data.productName} đã hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dịch vụ đã hết hạn</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .expired-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .expired-icon { font-size: 24px; margin-bottom: 10px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { margin: 20px 0; padding: 15px; background-color: #e3f2fd; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ Dịch vụ đã hết hạn</h1>
            <p>Thông báo quan trọng về tài khoản của bạn</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="expired-box">
                <div class="expired-icon">⏰</div>
                <h3>Dịch vụ đã hết hạn!</h3>
                <p>Dịch vụ của bạn đã hết hạn và hiện tại không thể sử dụng được.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                ${data.price ? `<p><strong>Giá gia hạn:</strong> ${data.price}</p>` : ''}
            </div>
            
            <p>Để khôi phục và tiếp tục sử dụng dịch vụ, vui lòng liên hệ với chúng tôi để thực hiện gia hạn.</p>
            
            <div style="text-align: center;">
                <a href="#" class="cta-button">🔄 Gia hạn ngay</a>
            </div>
            
            ${data.contactInfo ? `
            <div class="contact-info">
                <h4>📞 Thông tin liên hệ</h4>
                <p><strong>Điện thoại:</strong> ${data.contactInfo.phone}</p>
                <p><strong>Email:</strong> ${data.contactInfo.email}</p>
                ${data.contactInfo.address ? `<p><strong>Địa chỉ:</strong> ${data.contactInfo.address}</p>` : ''}
            </div>
            ` : ''}
            
            <p>Chúng tôi rất mong được tiếp tục phục vụ bạn!</p>
            
            <p>Trân trọng,<br>
            <strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Vui lòng không trả lời trực tiếp email này.</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
Xin chào ${data.customerName},

Dịch vụ ${data.productName} của bạn đã hết hạn vào ngày ${data.expiryDate}.

Để khôi phục và tiếp tục sử dụng dịch vụ, vui lòng liên hệ với chúng tôi để thực hiện gia hạn.

${data.contactInfo ? `
Thông tin liên hệ:
- Điện thoại: ${data.contactInfo.phone}
- Email: ${data.contactInfo.email}
- Địa chỉ: ${data.contactInfo.address}
` : ''}

Chúng tôi rất mong được tiếp tục phục vụ bạn!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  // Template cảnh báo quá hạn và chuẩn bị xóa tài khoản
  overdueWarning: (data: EmailTemplateData) => ({
    subject: `🚨 CẢNH BÁO: Tài khoản ${data.productName} quá hạn ${data.daysOverdue} ngày`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cảnh báo tài khoản quá hạn</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .warning-box { background-color: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .warning-icon { font-size: 32px; margin-bottom: 10px; }
        .danger-box { background-color: #f8d7da; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { margin: 20px 0; padding: 15px; background-color: #e3f2fd; border-radius: 8px; }
        .countdown { font-size: 18px; font-weight: bold; color: #dc3545; text-align: center; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 CẢNH BÁO QUAN TRỌNG</h1>
            <p>Tài khoản của bạn có nguy cơ bị xóa</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="warning-box">
                <div class="warning-icon">⚠️</div>
                <h3>Tài khoản quá hạn!</h3>
                <p>Dịch vụ của bạn đã quá hạn <strong>${data.daysOverdue} ngày</strong> và chưa được gia hạn.</p>
            </div>
            
            <div class="danger-box">
                <h3>🗑️ Thông báo quan trọng về việc xóa tài khoản</h3>
                <p>Nếu không gia hạn trong vòng <strong>7 ngày tới</strong>, tài khoản của bạn sẽ bị <strong style="color: #dc3545;">XÓA VĨNH VIỄN</strong> khỏi hệ thống.</p>
                <div class="countdown">⏰ Còn lại: 7 ngày</div>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Số ngày quá hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.daysOverdue} ngày</span></p>
                ${data.price ? `<p><strong>Giá gia hạn:</strong> ${data.price}</p>` : ''}
            </div>
            
            <p><strong>Hành động cần thiết:</strong></p>
            <ul>
                <li>Liên hệ ngay với chúng tôi để gia hạn dịch vụ</li>
                <li>Thực hiện thanh toán để khôi phục tài khoản</li>
                <li>Sao lưu dữ liệu quan trọng (nếu có)</li>
            </ul>
            
            <div style="text-align: center;">
                <a href="#" class="cta-button">🚨 GIA HẠN KHẨN CẤP</a>
            </div>
            
            ${data.contactInfo ? `
            <div class="contact-info">
                <h4>📞 Liên hệ khẩn cấp</h4>
                <p><strong>Điện thoại:</strong> ${data.contactInfo.phone}</p>
                <p><strong>Email:</strong> ${data.contactInfo.email}</p>
                ${data.contactInfo.address ? `<p><strong>Địa chỉ:</strong> ${data.contactInfo.address}</p>` : ''}
                <p style="color: #dc3545;"><strong>Vui lòng liên hệ ngay để tránh mất tài khoản!</strong></p>
            </div>
            ` : ''}
            
            <p>Chúng tôi không muốn mất bạn và rất mong được tiếp tục phục vụ!</p>
            
            <p>Trân trọng,<br>
            <strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Đây là thông báo quan trọng, vui lòng không bỏ qua!</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
🚨 CẢNH BÁO QUAN TRỌNG

Xin chào ${data.customerName},

Dịch vụ ${data.productName} của bạn đã quá hạn ${data.daysOverdue} ngày và chưa được gia hạn.

⚠️ THÔNG BÁO QUAN TRỌNG:
Nếu không gia hạn trong vòng 7 ngày tới, tài khoản của bạn sẽ bị XÓA VĨNH VIỄN khỏi hệ thống.

Thông tin dịch vụ:
- Tên dịch vụ: ${data.productName}
- Ngày hết hạn: ${data.expiryDate}
- Số ngày quá hạn: ${data.daysOverdue} ngày

Hành động cần thiết:
- Liên hệ ngay với chúng tôi để gia hạn dịch vụ
- Thực hiện thanh toán để khôi phục tài khoản
- Sao lưu dữ liệu quan trọng (nếu có)

${data.contactInfo ? `
Liên hệ khẩn cấp:
- Điện thoại: ${data.contactInfo.phone}
- Email: ${data.contactInfo.email}
- Địa chỉ: ${data.contactInfo.address}

VUI LÒNG LIÊN HỆ NGAY ĐỂ TRÁNH MẤT TÀI KHOẢN!
` : ''}

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  // Template thông báo gia hạn thành công
  renewalSuccess: (data: EmailTemplateData) => ({
    subject: `✅ Gia hạn thành công: ${data.productName}`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gia hạn thành công</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .success-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
        .success-icon { font-size: 48px; margin-bottom: 15px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .highlight { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Gia hạn thành công!</h1>
            <p>Cảm ơn bạn đã tin tưởng dịch vụ của chúng tôi</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="success-box">
                <div class="success-icon">🎉</div>
                <h3>Chúc mừng! Gia hạn thành công</h3>
                <p>Dịch vụ của bạn đã được gia hạn thành công và đang hoạt động bình thường.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin gia hạn</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                ${data.startDate ? `<p><strong>Ngày bắt đầu:</strong> ${data.startDate}</p>` : ''}
                <p><strong>Ngày hết hạn mới:</strong> <span style="color: #28a745; font-weight: 600;">${data.expiryDate}</span></p>
                ${data.price ? `<p><strong>Số tiền đã thanh toán:</strong> ${data.price}</p>` : ''}
            </div>
            
            <div class="highlight">
                <h4>🔥 Những gì bạn có thể làm ngay bây giờ:</h4>
                <ul>
                    <li>Tiếp tục sử dụng dịch vụ mà không bị gián đoạn</li>
                    <li>Truy cập đầy đủ các tính năng premium</li>
                    <li>Nhận hỗ trợ ưu tiên từ đội ngũ của chúng tôi</li>
                </ul>
            </div>
            
            <p>Dịch vụ của bạn sẽ tiếp tục hoạt động cho đến ngày <strong>${data.expiryDate}</strong>. Chúng tôi sẽ gửi thông báo nhắc nhở trước khi dịch vụ hết hạn.</p>
            
            ${data.contactInfo ? `
            <div class="contact-info">
                <h4>📞 Hỗ trợ khách hàng</h4>
                <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi:</p>
                <p><strong>Điện thoại:</strong> ${data.contactInfo.phone}</p>
                <p><strong>Email:</strong> ${data.contactInfo.email}</p>
                ${data.contactInfo.address ? `<p><strong>Địa chỉ:</strong> ${data.contactInfo.address}</p>` : ''}
            </div>
            ` : ''}
            
            <p>Cảm ơn bạn đã tiếp tục tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br>
            <strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Chúc bạn có trải nghiệm tuyệt vời với dịch vụ!</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
✅ GIA HẠN THÀNH CÔNG!

Xin chào ${data.customerName},

Chúc mừng! Dịch vụ ${data.productName} của bạn đã được gia hạn thành công.

Thông tin gia hạn:
- Tên dịch vụ: ${data.productName}
${data.startDate ? `- Ngày bắt đầu: ${data.startDate}` : ''}
- Ngày hết hạn mới: ${data.expiryDate}
${data.price ? `- Số tiền đã thanh toán: ${data.price}` : ''}

Dịch vụ của bạn sẽ tiếp tục hoạt động cho đến ngày ${data.expiryDate}.

${data.contactInfo ? `
Hỗ trợ khách hàng:
- Điện thoại: ${data.contactInfo.phone}
- Email: ${data.contactInfo.email}
- Địa chỉ: ${data.contactInfo.address}
` : ''}

Cảm ơn bạn đã tiếp tục tin tưởng và sử dụng dịch vụ của chúng tôi!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  // Template thông báo thay đổi giá dịch vụ
  priceChange: (data: EmailTemplateData) => ({
    subject: `📢 Thông báo: Thay đổi giá dịch vụ ${data.productName}`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông báo thay đổi giá</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .info-box { background-color: #e7f3ff; border: 1px solid #b3d7ff; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .price-comparison { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .price-row { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .old-price { background-color: #f8d7da; color: #721c24; }
        .new-price { background-color: #d4edda; color: #155724; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .contact-info { margin: 20px 0; padding: 15px; background-color: #e3f2fd; border-radius: 8px; }
        .effective-date { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📢 Thông báo quan trọng</h1>
            <p>Cập nhật giá dịch vụ</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="info-box">
                <h3>💰 Thông báo thay đổi giá dịch vụ</h3>
                <p>Chúng tôi muốn thông báo với bạn về việc điều chỉnh giá dịch vụ <strong>${data.productName}</strong>.</p>
            </div>
            
            <div class="price-comparison">
                <h3>📊 So sánh giá</h3>
                ${data.oldPrice ? `
                <div class="price-row old-price">
                    <span><strong>Giá cũ:</strong></span>
                    <span><strong>${data.oldPrice}</strong></span>
                </div>
                ` : ''}
                ${data.newPrice ? `
                <div class="price-row new-price">
                    <span><strong>Giá mới:</strong></span>
                    <span><strong>${data.newPrice}</strong></span>
                </div>
                ` : ''}
            </div>
            
            ${data.effectiveDate ? `
            <div class="effective-date">
                <h4>📅 Ngày có hiệu lực</h4>
                <p><strong>${data.effectiveDate}</strong></p>
                <p><em>Giá mới sẽ áp dụng cho các lần gia hạn từ ngày này trở đi</em></p>
            </div>
            ` : ''}
            
            <div class="service-info">
                <h3>📋 Thông tin quan trọng</h3>
                <ul>
                    <li><strong>Dịch vụ hiện tại của bạn không bị ảnh hưởng</strong> - Bạn sẽ tiếp tục sử dụng với giá đã thanh toán cho đến hết hạn</li>
                    <li>Giá mới chỉ áp dụng khi bạn gia hạn dịch vụ</li>
                    <li>Chúng tôi cam kết duy trì chất lượng dịch vụ tốt nhất</li>
                    <li>Đội ngũ hỗ trợ luôn sẵn sàng giải đáp thắc mắc của bạn</li>
                </ul>
            </div>
            
            <p><strong>Lý do thay đổi giá:</strong></p>
            <p>Việc điều chỉnh giá này nhằm đảm bảo chúng tôi có thể tiếp tục cung cấp dịch vụ chất lượng cao, cập nhật các tính năng mới và duy trì hỗ trợ khách hàng tốt nhất.</p>
            
            ${data.contactInfo ? `
            <div class="contact-info">
                <h4>📞 Liên hệ tư vấn</h4>
                <p>Nếu bạn có bất kỳ câu hỏi nào về thay đổi giá này, vui lòng liên hệ:</p>
                <p><strong>Điện thoại:</strong> ${data.contactInfo.phone}</p>
                <p><strong>Email:</strong> ${data.contactInfo.email}</p>
                ${data.contactInfo.address ? `<p><strong>Địa chỉ:</strong> ${data.contactInfo.address}</p>` : ''}
            </div>
            ` : ''}
            
            <p>Cảm ơn bạn đã hiểu và tiếp tục ủng hộ dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br>
            <strong>Ban Quản lý</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Thông báo này có hiệu lực từ ngày gửi.</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
📢 THÔNG BÁO THAY ĐỔI GIÁ DỊCH VỤ

Xin chào ${data.customerName},

Chúng tôi muốn thông báo với bạn về việc điều chỉnh giá dịch vụ ${data.productName}.

So sánh giá:
${data.oldPrice ? `- Giá cũ: ${data.oldPrice}` : ''}
${data.newPrice ? `- Giá mới: ${data.newPrice}` : ''}

${data.effectiveDate ? `
Ngày có hiệu lực: ${data.effectiveDate}
Giá mới sẽ áp dụng cho các lần gia hạn từ ngày này trở đi.
` : ''}

Thông tin quan trọng:
- Dịch vụ hiện tại của bạn không bị ảnh hưởng
- Giá mới chỉ áp dụng khi bạn gia hạn dịch vụ
- Chúng tôi cam kết duy trì chất lượng dịch vụ tốt nhất

${data.contactInfo ? `
Liên hệ tư vấn:
- Điện thoại: ${data.contactInfo.phone}
- Email: ${data.contactInfo.email}
- Địa chỉ: ${data.contactInfo.address}
` : ''}

Cảm ơn bạn đã hiểu và tiếp tục ủng hộ dịch vụ của chúng tôi!

Trân trọng,
Ban Quản lý
    `
  })
}

// Hàm helper để tạo email với template
export const createEmailFromTemplate = (
  templateType: keyof typeof emailTemplates,
  data: EmailTemplateData
) => {
  const template = emailTemplates[templateType](data)
  return template
}

// Hàm helper để lấy thông tin liên hệ mặc định
export const getDefaultContactInfo = () => ({
  phone: '******-456-7041',
  email: '<EMAIL>',
  address: '1600 Pennsylvania Ave NW, Washington, DC 20500, Hoa Kỳ'
})