{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@supabase/supabase-js": "^2.50.3", "date-fns": "^4.1.0", "lucide-react": "^0.344.0", "nodemailer": "^7.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-router-dom": "^7.6.3", "twilio": "^5.7.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}