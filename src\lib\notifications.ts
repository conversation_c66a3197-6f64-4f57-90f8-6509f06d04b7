import { settingsService } from './database'
import { emailTemplates, createEmailFromTemplate, getDefaultContactInfo, type EmailTemplateData } from './emailTemplates'

export interface EmailConfig {
  smtpHost: string
  smtpPort: string
  smtpUser: string
  smtpPassword: string
  fromEmail: string
  fromName: string
}

export interface SmsConfig {
  twilioAccountSid: string
  twilioAuthToken: string
  twilioPhoneNumber: string
}

export interface NotificationConfig {
  emailEnabled: boolean
  smsEnabled: boolean
  reminderDays: number
  autoReminder: boolean
}

export const notificationService = {
  // SMS Templates in Vietnamese
  getSmsTemplate(type: 'reminder' | 'expired' | 'welcome' | 'renewal' | 'overdue', data: any): string {
    switch (type) {
      case 'reminder':
        return `🔔 Xin chào ${data.customerName}! Dịch vụ ${data.productName} sẽ hết hạn vào ${data.expiryDate}. Vui lòng liên hệ để gia hạn. Cảm ơn!`
      
      case 'expired':
        return `❌ Thông báo: Dịch vụ ${data.productName} của ${data.customerName} đã hết hạn vào ${data.expiryDate}. Liên hệ ngay để gia hạn!`
      
      case 'welcome':
        return `🎉 Chào mừng ${data.customerName}! Đăng ký ${data.productName} thành công. Thời hạn đến ${data.expiryDate}. Cảm ơn bạn đã tin tưởng!`
      
      case 'renewal':
        return `✅ ${data.customerName} ơi! Gia hạn ${data.productName} thành công đến ${data.expiryDate}. Cảm ơn bạn đã tiếp tục sử dụng dịch vụ!`
      
      case 'overdue':
        return `🚨 CẢNH BÁO: ${data.customerName}, dịch vụ ${data.productName} đã quá hạn ${data.daysOverdue} ngày. Tài khoản có nguy cơ bị xóa. Liên hệ NGAY!`
      
      default:
        return `Thông báo từ hệ thống quản lý thuê bao.`
    }
  },

  async sendTestEmail(to: string, subject: string, message: string): Promise<{ success: boolean; message: string }> {
    try {
      const emailConfig: EmailConfig = await settingsService.get('email_config')
      
      if (!emailConfig.smtpHost || !emailConfig.smtpUser || !emailConfig.smtpPassword) {
        return { success: false, message: 'Cấu hình email chưa đầy đủ' }
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-email`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to,
          subject,
          message,
          config: emailConfig
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      return { success: true, message: 'Email đã được gửi thành công!' }
    } catch (error) {
      console.error('Email sending error:', error)
      return { success: false, message: 'Lỗi khi gửi email: ' + (error as Error).message }
    }
  },

  async sendTestSms(to: string, message: string): Promise<{ success: boolean; message: string }> {
    try {
      const smsConfig: SmsConfig = await settingsService.get('sms_config')
      
      if (!smsConfig.twilioAccountSid || !smsConfig.twilioAuthToken || !smsConfig.twilioPhoneNumber) {
        return { success: false, message: 'Cấu hình SMS chưa đầy đủ' }
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-notification`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'sms',
          to,
          message,
          config: smsConfig
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      return { success: true, message: 'SMS đã được gửi thành công!' }
    } catch (error) {
      console.error('SMS sending error:', error)
      return { success: false, message: 'Lỗi khi gửi SMS: ' + (error as Error).message }
    }
  },

  async sendExpirationReminder(customerEmail: string, customerPhone: string, customerName: string, productName: string, expiryDate: string): Promise<void> {
    const notificationConfig: NotificationConfig = await settingsService.get('notification_config')
    
    const templateData: EmailTemplateData = {
      customerName,
      productName,
      expiryDate,
      contactInfo: getDefaultContactInfo()
    }
    
    const emailTemplate = createEmailFromTemplate('expirationReminder', templateData)
    const smsMessage = this.getSmsTemplate('reminder', { customerName, productName, expiryDate })

    if (notificationConfig.emailEnabled && customerEmail) {
      await this.sendEmailWithTemplate(customerEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text)
    }

    if (notificationConfig.smsEnabled && customerPhone) {
      await this.sendTestSms(customerPhone, smsMessage)
    }
  },

  async sendExpiredNotification(customerEmail: string, customerPhone: string, customerName: string, productName: string, expiryDate: string): Promise<void> {
    const notificationConfig: NotificationConfig = await settingsService.get('notification_config')
    
    const templateData: EmailTemplateData = {
      customerName,
      productName,
      expiryDate,
      contactInfo: getDefaultContactInfo()
    }
    
    const emailTemplate = createEmailFromTemplate('expired', templateData)
    const smsMessage = this.getSmsTemplate('expired', { customerName, productName, expiryDate })

    if (notificationConfig.emailEnabled && customerEmail) {
      await this.sendEmailWithTemplate(customerEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text)
    }

    if (notificationConfig.smsEnabled && customerPhone) {
      await this.sendTestSms(customerPhone, smsMessage)
    }
  },

  async sendOverdueWarning(customerEmail: string, customerPhone: string, customerName: string, productName: string, expiryDate: string, daysOverdue: number): Promise<void> {
    const notificationConfig: NotificationConfig = await settingsService.get('notification_config')
    
    const templateData: EmailTemplateData = {
      customerName,
      productName,
      expiryDate,
      daysOverdue,
      contactInfo: getDefaultContactInfo()
    }
    
    const emailTemplate = createEmailFromTemplate('overdueWarning', templateData)
    const smsMessage = this.getSmsTemplate('overdue', { customerName, productName, expiryDate, daysOverdue })

    if (notificationConfig.emailEnabled && customerEmail) {
      await this.sendEmailWithTemplate(customerEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text)
    }

    if (notificationConfig.smsEnabled && customerPhone) {
      await this.sendTestSms(customerPhone, smsMessage)
    }
  },

  async sendRenewalSuccess(customerEmail: string, customerPhone: string, customerName: string, productName: string, startDate: string, expiryDate: string, price: string): Promise<void> {
    const notificationConfig: NotificationConfig = await settingsService.get('notification_config')
    
    const templateData: EmailTemplateData = {
      customerName,
      productName,
      startDate,
      expiryDate,
      price,
      contactInfo: getDefaultContactInfo()
    }
    
    const emailTemplate = createEmailFromTemplate('renewalSuccess', templateData)
    const smsMessage = this.getSmsTemplate('renewal', { customerName, productName, expiryDate })

    if (notificationConfig.emailEnabled && customerEmail) {
      await this.sendEmailWithTemplate(customerEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text)
    }

    if (notificationConfig.smsEnabled && customerPhone) {
      await this.sendTestSms(customerPhone, smsMessage)
    }
  },

  async sendPriceChangeNotification(customerEmail: string, customerName: string, productName: string, oldPrice: string, newPrice: string, effectiveDate: string): Promise<void> {
    const notificationConfig: NotificationConfig = await settingsService.get('notification_config')
    
    const templateData: EmailTemplateData = {
      customerName,
      productName,
      oldPrice,
      newPrice,
      effectiveDate,
      contactInfo: getDefaultContactInfo()
    }
    
    const emailTemplate = createEmailFromTemplate('priceChange', templateData)

    if (notificationConfig.emailEnabled && customerEmail) {
      await this.sendEmailWithTemplate(customerEmail, emailTemplate.subject, emailTemplate.html, emailTemplate.text)
    }
  },

  async sendEmailWithTemplate(to: string, subject: string, htmlContent: string, textContent: string): Promise<{ success: boolean; message: string }> {
    try {
      const emailConfig: EmailConfig = await settingsService.get('email_config')
      
      if (!emailConfig.smtpHost || !emailConfig.smtpUser || !emailConfig.smtpPassword) {
        return { success: false, message: 'Cấu hình email chưa đầy đủ' }
      }

      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-email`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to,
          subject,
          htmlContent,
          textContent,
          config: emailConfig
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      return { success: true, message: 'Email đã được gửi thành công!' }
    } catch (error) {
      console.error('Email sending error:', error)
      return { success: false, message: 'Lỗi khi gửi email: ' + (error as Error).message }
    }
  }
}