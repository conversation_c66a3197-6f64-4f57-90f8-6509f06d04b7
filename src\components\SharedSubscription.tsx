import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { supabase, type SubscriptionWithRelations } from '../lib/database'
import { formatCurrency, formatDate, getStatusColor, getStatusText } from '../lib/utils'
import { Calendar, User, Package, DollarSign, Clock, Eye, AlertCircle } from 'lucide-react'

export function SharedSubscription() {
  const { id } = useParams<{ id: string }>()
  const [subscription, setSubscription] = useState<SubscriptionWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageType, setImageType] = useState<'buyer' | 'seller' | null>(null)

  useEffect(() => {
    if (id) {
      fetchSubscription(id)
    }
  }, [id])

  const fetchSubscription = async (subscriptionId: string) => {
    try {
      const { data: subscription, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          customers(name, email, phone),
          products(name, service_type, duration_months, current_price)
        `)
        .eq('id', subscriptionId)
        .single()
      
      if (error) {
        console.error('Supabase error:', error)
        setError('Không tìm thấy thông tin đơn hàng')
      } else if (subscription) {
        setSubscription(subscription)
      } else {
        setError('Không tìm thấy thông tin đơn hàng')
      }
    } catch (error) {
      console.error('Error fetching subscription:', error)
      setError('Lỗi khi tải thông tin đơn hàng')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thông tin đơn hàng...</p>
        </div>
      </div>
    )
  }

  if (error || !subscription) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Không tìm thấy đơn hàng</h1>
          <p className="text-gray-600 mb-4">
            {error || 'Đơn hàng không tồn tại hoặc đã bị xóa.'}
          </p>
          <button
            onClick={() => window.close()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Đóng trang
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Thông tin đơn hàng</h1>
            <p className="text-gray-600">Chi tiết giao dịch và thông tin thanh toán</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-4">
              <User className="h-5 w-5 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900">Thông tin khách hàng</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Tên khách hàng</label>
                <p className="text-lg font-medium text-gray-900">{subscription.customers.name}</p>
              </div>
              
              {subscription.customers.email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-gray-900">{subscription.customers.email}</p>
                </div>
              )}
              
              {subscription.customers.phone && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Số điện thoại</label>
                  <p className="text-gray-900">{subscription.customers.phone}</p>
                </div>
              )}
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-4">
              <Package className="h-5 w-5 text-green-600" />
              <h2 className="text-xl font-semibold text-gray-900">Thông tin sản phẩm</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Tên sản phẩm</label>
                <p className="text-lg font-medium text-gray-900">{subscription.products.name}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Loại dịch vụ</label>
                <p className="text-gray-900">{subscription.products.service_type}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Thời hạn</label>
                <p className="text-gray-900">{subscription.products.duration_months} tháng</p>
              </div>
            </div>
          </div>

          {/* Transaction Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-4">
              <Calendar className="h-5 w-5 text-purple-600" />
              <h2 className="text-xl font-semibold text-gray-900">Thông tin giao dịch</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Ngày mua</label>
                <p className="text-gray-900">{formatDate(subscription.purchase_date)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Ngày bắt đầu</label>
                <p className="text-gray-900">{formatDate(subscription.start_date)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Ngày hết hạn</label>
                <p className="text-gray-900">{formatDate(subscription.expiry_date)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Trạng thái</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                  {getStatusText(subscription.status)}
                </span>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-2 mb-4">
              <DollarSign className="h-5 w-5 text-yellow-600" />
              <h2 className="text-xl font-semibold text-gray-900">Thông tin thanh toán</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Số tiền đã thanh toán</label>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(subscription.price_paid)}</p>
              </div>
              
              {subscription.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Ghi chú</label>
                  <p className="text-gray-900">{subscription.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Transaction Images */}
        {(subscription.buyer_transaction_image || subscription.seller_transaction_image) && (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Eye className="h-5 w-5 text-indigo-600" />
              <h2 className="text-xl font-semibold text-gray-900">Bằng chứng chuyển khoản</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {subscription.buyer_transaction_image && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Chuyển khoản từ khách hàng</h3>
                  <div className="relative">
                    <img
                      src={subscription.buyer_transaction_image}
                      alt="Customer transaction proof"
                      className="w-full h-64 object-cover rounded-lg border cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setImagePreview(subscription.buyer_transaction_image)
                        setImageType('buyer')
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                      <Eye className="h-8 w-8 text-white" />
                    </div>
                  </div>
                </div>
              )}
              
              {subscription.seller_transaction_image && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Chuyển khoản từ người bán</h3>
                  <div className="relative">
                    <img
                      src={subscription.seller_transaction_image}
                      alt="Seller transaction proof"
                      className="w-full h-64 object-cover rounded-lg border cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => {
                        setImagePreview(subscription.seller_transaction_image)
                        setImageType('seller')
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                      <Eye className="h-8 w-8 text-white" />
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>💡 Lưu ý:</strong> Click vào hình ảnh để xem phóng to. Đây là bằng chứng giao dịch được lưu trữ an toàn trong hệ thống.
              </p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-8 p-4 bg-white rounded-lg shadow-sm">
          <div className="mb-3 p-3 bg-green-50 rounded-lg">
            <p className="text-sm text-green-800 font-medium">
              ✅ Thông tin này đã được xác thực và lưu trữ an toàn
            </p>
          </div>
          <p className="text-sm text-gray-500">
            Thông tin này được chia sẻ từ hệ thống quản lý thuê bao
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Được tạo vào {new Date().toLocaleDateString('vi-VN')}
          </p>
          <div className="mt-3 flex justify-center gap-4 text-xs text-gray-400">
            <span>🔒 Bảo mật</span>
            <span>📱 Responsive</span>
            <span>🇻🇳 Tiếng Việt</span>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {imagePreview && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {imageType === 'buyer' ? 'Chuyển khoản từ khách hàng' : 'Chuyển khoản từ người bán'}
              </h3>
              <button
                onClick={() => {
                  setImagePreview(null)
                  setImageType(null)
                }}
                className="text-gray-500 hover:text-gray-700 text-2xl"
              >
                ✕
              </button>
            </div>
            <img
              src={imagePreview}
              alt="Transaction proof"
              className="max-w-full max-h-[70vh] object-contain mx-auto"
            />
          </div>
        </div>
      )}
    </div>
  )
}