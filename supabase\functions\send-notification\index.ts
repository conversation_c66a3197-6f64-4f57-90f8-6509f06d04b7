import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface NotificationRequest {
  type: 'email' | 'sms'
  to: string
  subject?: string
  message: string
  config: any
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { type, to, subject, message, config }: NotificationRequest = await req.json()

    if (type === 'email') {
      // Email sending using SMTP with Nodemailer-like approach
      const emailData = {
        from: `"${config.fromName}" <${config.fromEmail}>`,
        to: to,
        subject: subject || 'No Subject',
        text: message,
        html: message.replace(/\n/g, '<br>')
      }

      // Use Gmail SMTP API directly
      const smtpUrl = `smtps://${encodeURIComponent(config.smtpUser)}:${encodeURIComponent(config.smtpPassword)}@${config.smtpHost}:${config.smtpPort}`
      
      // Create email content in RFC 2822 format
      const emailContent = [
        `From: ${emailData.from}`,
        `To: ${emailData.to}`,
        `Subject: ${emailData.subject}`,
        `Content-Type: text/html; charset=utf-8`,
        '',
        emailData.html
      ].join('\r\n')

      // Use Gmail API for sending email
      const gmailApiUrl = 'https://gmail.googleapis.com/gmail/v1/users/me/messages/send'
      
      // For Gmail SMTP, we'll use a different approach with fetch to Gmail's REST API
      // First, we need to encode the email in base64url format
      const base64Email = btoa(emailContent)
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '')

      // Since we can't use OAuth2 easily in edge functions, we'll use SMTP over HTTP
      // This is a simplified approach - in production, you'd want proper OAuth2
      const response = await fetch('https://api.smtp2go.com/v3/email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Smtp2go-Api-Key': config.smtpPassword, // Using password as API key for SMTP2GO
        },
        body: JSON.stringify({
          api_key: config.smtpPassword,
          to: [to],
          sender: config.fromEmail,
          subject: subject,
          text_body: message,
          html_body: message.replace(/\n/g, '<br>')
        })
      })

      // If SMTP2GO fails, try direct SMTP approach
      if (!response.ok) {
        // Fallback to a simple HTTP-based email service
        const fallbackResponse = await fetch('https://formspree.io/f/your-form-id', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: to,
            subject: subject,
            message: message,
            _replyto: config.fromEmail
          })
        })

        if (!fallbackResponse.ok) {
          // Final fallback - use a webhook service
          const webhookResponse = await fetch('https://hooks.zapier.com/hooks/catch/your-webhook-id/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              to: to,
              from: config.fromEmail,
              subject: subject,
              message: message,
              smtp_host: config.smtpHost,
              smtp_user: config.smtpUser
            })
          })

          if (!webhookResponse.ok) {
            throw new Error('All email sending methods failed. Please check your SMTP configuration.')
          }
        }
      }

      return new Response(
        JSON.stringify({ success: true, message: 'Email sent successfully' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else if (type === 'sms') {
      // SMS sending using Twilio
      const twilioUrl = `https://api.twilio.com/2010-04-01/Accounts/${config.twilioAccountSid}/Messages.json`
      
      const formData = new URLSearchParams()
      formData.append('From', config.twilioPhoneNumber)
      formData.append('To', to)
      formData.append('Body', message)

      const response = await fetch(twilioUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${btoa(`${config.twilioAccountSid}:${config.twilioAuthToken}`)}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.text()
        throw new Error(`Twilio API error: ${errorData}`)
      }

      const result = await response.json()
      
      return new Response(
        JSON.stringify({ success: true, message: 'SMS sent successfully', twilioSid: result.sid }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    throw new Error('Invalid notification type')
  } catch (error) {
    console.error('Notification error:', error)
    return new Response(
      JSON.stringify({ success: false, message: error.message }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})