import React from 'react'
import { useAuth } from '../hooks/useAuth'
import { LogIn, Shield } from 'lucide-react'

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading, signInWithGoogle } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
          <div className="text-center">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Hệ thống quản lý thuê bao
            </h1>
            <p className="text-gray-600 mb-6">
              Chỉ dành cho quản trị viên. Vui lòng đăng nhập để tiếp tục.
            </p>
            <button
              onClick={signInWithGoogle}
              className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <LogIn className="h-5 w-5" />
              Đăng nhập bằng Google
            </button>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}