import { supabase } from './supabase'
import type { Database } from './supabase'

export type Customer = Database['public']['Tables']['customers']['Row']
export type Product = Database['public']['Tables']['products']['Row']
export type Subscription = Database['public']['Tables']['subscriptions']['Row']
export type PriceHistory = Database['public']['Tables']['price_history']['Row']

export interface Setting {
  id: string
  key: string
  value: any
  description: string | null
  created_at: string
  updated_at: string
}

export interface SubscriptionWithRelations extends Subscription {
  customers: {
    name: string
    email: string | null
    phone: string | null
  }
  products: {
    name: string
    service_type: string
    duration_months: number
  }
}

// Export supabase for direct use in components when needed
export { supabase }
// Customer operations
export const customerService = {
  async getAll(): Promise<Customer[]> {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(customer: Omit<Customer, 'id' | 'created_at' | 'updated_at'>): Promise<Customer> {
    const { data, error } = await supabase
      .from('customers')
      .insert([customer])
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, customer: Partial<Customer>): Promise<Customer> {
    const { data, error } = await supabase
      .from('customers')
      .update({ ...customer, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Product operations
export const productService = {
  async getAll(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<Product> {
    const { data: newProduct, error } = await supabase
      .from('products')
      .insert([product])
      .select()
      .single()

    if (error) throw error

    // Add initial price to history
    await supabase
      .from('price_history')
      .insert([{
        product_id: newProduct.id,
        price: product.current_price,
        effective_date: new Date().toISOString()
      }])

    return newProduct
  },

  async update(id: string, product: Partial<Product>, originalPrice: number): Promise<Product> {
    // If price changed, add to price history
    if (product.current_price && product.current_price !== originalPrice) {
      await supabase
        .from('price_history')
        .insert([{
          product_id: id,
          price: product.current_price,
          effective_date: new Date().toISOString()
        }])
    }

    const { data, error } = await supabase
      .from('products')
      .update({ ...product, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async getPriceHistory(productId: string): Promise<PriceHistory[]> {
    const { data, error } = await supabase
      .from('price_history')
      .select('*')
      .eq('product_id', productId)
      .order('effective_date', { ascending: false })

    if (error) throw error
    return data || []
  }
}

// Subscription operations
export const subscriptionService = {
  async getAll(): Promise<SubscriptionWithRelations[]> {
    const { data, error } = await supabase
      .from('subscriptions')
      .select(`
        *,
        customers(name, email, phone),
        products(name, service_type, duration_months)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(subscription: Omit<Subscription, 'id' | 'created_at' | 'updated_at'>): Promise<Subscription> {
    const { data, error } = await supabase
      .from('subscriptions')
      .insert([subscription])
      .select()
      .single()

    if (error) throw error

    // Send welcome email
    try {
      await fetch('/.netlify/functions/send-welcome-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: data.id
        })
      })
    } catch (error) {
      console.error('Failed to send welcome email:', error)
      // Don't throw error here as subscription was created successfully
    }

    return data
  },

  async update(id: string, subscription: Partial<Subscription>): Promise<Subscription> {
    const { data, error } = await supabase
      .from('subscriptions')
      .update({ ...subscription, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('subscriptions')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async uploadImage(file: File, subscriptionId: string, type: 'buyer' | 'seller'): Promise<string> {
    const fileExt = file.name.split('.').pop()
    const fileName = `${subscriptionId}-${type}-${Date.now()}.${fileExt}`
    
    const { error: uploadError } = await supabase.storage
      .from('transaction-images')
      .upload(fileName, file)

    if (uploadError) throw uploadError

    const { data } = supabase.storage
      .from('transaction-images')
      .getPublicUrl(fileName)

    const column = type === 'buyer' ? 'buyer_transaction_image' : 'seller_transaction_image'
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({ [column]: data.publicUrl })
      .eq('id', subscriptionId)

    if (updateError) throw updateError

    return data.publicUrl
  }
}

// Settings operations
export const settingsService = {
  async get(key: string): Promise<any> {
    const { data, error } = await supabase
      .from('settings')
      .select('value')
      .eq('key', key)
      .single()

    if (error) throw error
    return data?.value
  },

  async set(key: string, value: any): Promise<void> {
    // First try to update existing setting
    const { data: existing } = await supabase
      .from('settings')
      .select('id')
      .eq('key', key)
      .single()

    if (existing) {
      // Update existing setting
      const { error } = await supabase
        .from('settings')
        .update({
          value,
          updated_at: new Date().toISOString()
        })
        .eq('key', key)

      if (error) throw error
    } else {
      // Insert new setting
      const { error } = await supabase
        .from('settings')
        .insert({
          key,
          value,
          updated_at: new Date().toISOString()
        })

      if (error) throw error
    }
  },

  async getAll(): Promise<Setting[]> {
    const { data, error } = await supabase
      .from('settings')
      .select('*')
      .order('key')

    if (error) throw error
    return data || []
  }
}