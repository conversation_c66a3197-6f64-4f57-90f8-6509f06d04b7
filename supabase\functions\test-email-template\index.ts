import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface TestEmailRequest {
  to: string
  template: string
}

// Sample data for testing templates
const getSampleData = (template: string) => {
  const baseData = {
    customerName: 'Nguyễn Văn A',
    productName: 'YouTube Premium',
    serviceType: 'Video Streaming',
    expiryDate: '15/02/2024',
    startDate: '15/01/2024',
    price: '79.000 ₫',
    durationMonths: 1,
    pricePaid: '79.000 ₫',
    contactInfo: {
      phone: '0123-456-789',
      email: '<EMAIL>',
      website: 'https://example.com'
    }
  }

  switch (template) {
    case 'welcome':
      return baseData
    case 'expirationReminder':
      return {
        ...baseData,
        daysLeft: 3
      }
    case 'expired':
      return baseData
    case 'overdueWarning':
      return {
        ...baseData,
        daysOverdue: 7
      }
    case 'renewalSuccess':
      return {
        ...baseData,
        newExpiryDate: '15/03/2024'
      }
    case 'priceChange':
      return {
        ...baseData,
        oldPrice: '69.000 ₫',
        newPrice: '79.000 ₫',
        effectiveDate: '01/03/2024'
      }
    default:
      return baseData
  }
}

// Email templates
const emailTemplates = {
  welcome: (data: any) => ({
    subject: `🎉 Chào mừng bạn đến với ${data.productName}!`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng bạn!</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .welcome-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
        .welcome-icon { font-size: 48px; margin-bottom: 15px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .highlight { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Welcome Email
        </div>
        
        <div class="header">
            <h1>🎉 Chào mừng bạn!</h1>
            <p>Cảm ơn bạn đã đăng ký dịch vụ của chúng tôi</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="welcome-box">
                <div class="welcome-icon">🎉</div>
                <h3>Đăng ký thành công!</h3>
                <p>Chúc mừng bạn đã đăng ký thành công dịch vụ <strong>${data.productName}</strong>!</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin đăng ký</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Loại dịch vụ:</strong> ${data.serviceType}</p>
                <p><strong>Ngày bắt đầu:</strong> ${data.startDate}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #28a745; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Thời hạn:</strong> ${data.durationMonths} tháng</p>
                <p><strong>Số tiền đã thanh toán:</strong> ${data.pricePaid}</p>
            </div>
            
            <div class="highlight">
                <h4>🔥 Những gì bạn có thể làm ngay bây giờ:</h4>
                <ul>
                    <li>Bắt đầu sử dụng dịch vụ ngay lập tức</li>
                    <li>Truy cập đầy đủ các tính năng premium</li>
                    <li>Nhận hỗ trợ ưu tiên từ đội ngũ của chúng tôi</li>
                </ul>
            </div>
            
            <p>Cảm ơn bạn đã tin tưởng và chọn dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  }),

  expirationReminder: (data: any) => ({
    subject: `🔔 Nhắc nhở: Dịch vụ ${data.productName} sắp hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhắc nhở gia hạn dịch vụ</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .alert-box { background-color: #fef3cd; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Expiration Reminder
        </div>
        
        <div class="header">
            <h1>🔔 Thông báo quan trọng</h1>
            <p>Dịch vụ của bạn sắp hết hạn</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="alert-box">
                <h3>⚠️ Dịch vụ sắp hết hạn!</h3>
                <p>Chúng tôi muốn nhắc nhở bạn rằng dịch vụ của bạn sẽ hết hạn trong ${data.daysLeft} ngày nữa.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Giá gia hạn:</strong> ${data.price}</p>
            </div>
            
            <p>Để tiếp tục sử dụng dịch vụ mà không bị gián đoạn, vui lòng liên hệ với chúng tôi để gia hạn trước ngày hết hạn.</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  }),

  expired: (data: any) => ({
    subject: `❌ Thông báo: Dịch vụ ${data.productName} đã hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dịch vụ đã hết hạn</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .expired-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Expired Notification
        </div>
        
        <div class="header">
            <h1>❌ Dịch vụ đã hết hạn</h1>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="expired-box">
                <h3>⏰ Dịch vụ đã hết hạn!</h3>
                <p>Dịch vụ của bạn đã hết hạn và hiện tại không thể sử dụng được.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Giá gia hạn:</strong> ${data.price}</p>
            </div>
            
            <p>Để khôi phục và tiếp tục sử dụng dịch vụ, vui lòng liên hệ với chúng tôi để thực hiện gia hạn.</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  }),

  overdueWarning: (data: any) => ({
    subject: `🚨 CẢNH BÁO: Tài khoản ${data.productName} quá hạn ${data.daysOverdue} ngày`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cảnh báo tài khoản quá hạn</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .warning-box { background-color: #fff3cd; border: 2px solid #ffc107; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .danger-box { background-color: #f8d7da; border: 2px solid #dc3545; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .countdown { font-size: 18px; font-weight: bold; color: #dc3545; text-align: center; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Overdue Warning
        </div>
        
        <div class="header">
            <h1>🚨 CẢNH BÁO QUAN TRỌNG</h1>
            <p>Tài khoản của bạn có nguy cơ bị xóa</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="warning-box">
                <h3>⚠️ Tài khoản quá hạn!</h3>
                <p>Dịch vụ của bạn đã quá hạn <strong>${data.daysOverdue} ngày</strong> và chưa được gia hạn.</p>
            </div>
            
            <div class="danger-box">
                <h3>🗑️ Thông báo quan trọng về việc xóa tài khoản</h3>
                <p>Nếu không gia hạn trong vòng <strong>7 ngày tới</strong>, tài khoản của bạn sẽ bị <strong style="color: #dc3545;">XÓA VĨNH VIỄN</strong> khỏi hệ thống.</p>
                <div class="countdown">⏰ Còn lại: 7 ngày</div>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Số ngày quá hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.daysOverdue} ngày</span></p>
            </div>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  }),

  renewalSuccess: (data: any) => ({
    subject: `✅ Gia hạn thành công: ${data.productName}`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gia hạn thành công</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .success-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
        .success-icon { font-size: 48px; margin-bottom: 15px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .highlight { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Renewal Success
        </div>
        
        <div class="header">
            <h1>✅ Gia hạn thành công!</h1>
            <p>Cảm ơn bạn đã tin tưởng dịch vụ của chúng tôi</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="success-box">
                <div class="success-icon">🎉</div>
                <h3>Chúc mừng! Gia hạn thành công</h3>
                <p>Dịch vụ của bạn đã được gia hạn thành công và đang hoạt động bình thường.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin gia hạn</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày bắt đầu:</strong> ${data.startDate}</p>
                <p><strong>Ngày hết hạn mới:</strong> <span style="color: #28a745; font-weight: 600;">${data.newExpiryDate || data.expiryDate}</span></p>
                <p><strong>Số tiền đã thanh toán:</strong> ${data.pricePaid}</p>
            </div>
            
            <div class="highlight">
                <h4>🔥 Những gì bạn có thể làm ngay bây giờ:</h4>
                <ul>
                    <li>Tiếp tục sử dụng dịch vụ mà không bị gián đoạn</li>
                    <li>Truy cập đầy đủ các tính năng premium</li>
                    <li>Nhận hỗ trợ ưu tiên từ đội ngũ của chúng tôi</li>
                </ul>
            </div>
            
            <p>Cảm ơn bạn đã tiếp tục tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  }),

  priceChange: (data: any) => ({
    subject: `📢 Thông báo: Thay đổi giá dịch vụ ${data.productName}`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông báo thay đổi giá</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .info-box { background-color: #e7f3ff; border: 1px solid #b3d7ff; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .price-comparison { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .price-row { display: flex; justify-content: space-between; align-items: center; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .old-price { background-color: #f8d7da; color: #721c24; }
        .new-price { background-color: #d4edda; color: #155724; }
        .effective-date { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 15px 0; text-align: center; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
        .test-banner { background-color: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; text-align: center; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-banner">
            <strong>🧪 ĐÂY LÀ EMAIL THỬ NGHIỆM</strong><br>
            Template: Price Change Notification
        </div>
        
        <div class="header">
            <h1>📢 Thông báo quan trọng</h1>
            <p>Cập nhật giá dịch vụ</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="info-box">
                <h3>💰 Thông báo thay đổi giá dịch vụ</h3>
                <p>Chúng tôi muốn thông báo với bạn về việc điều chỉnh giá dịch vụ <strong>${data.productName}</strong>.</p>
            </div>
            
            <div class="price-comparison">
                <h3>📊 So sánh giá</h3>
                <div class="price-row old-price">
                    <span><strong>Giá cũ:</strong></span>
                    <span><strong>${data.oldPrice}</strong></span>
                </div>
                <div class="price-row new-price">
                    <span><strong>Giá mới:</strong></span>
                    <span><strong>${data.newPrice}</strong></span>
                </div>
            </div>
            
            <div class="effective-date">
                <h4>📅 Ngày có hiệu lực</h4>
                <p><strong>${data.effectiveDate}</strong></p>
                <p><em>Giá mới sẽ áp dụng cho các lần gia hạn từ ngày này trở đi</em></p>
            </div>
            
            <p>Cảm ơn bạn đã hiểu và tiếp tục ủng hộ dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Ban Quản lý</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `
  })
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { to, template }: TestEmailRequest = await req.json()

    if (!to || !template) {
      throw new Error('Missing required parameters: to, template')
    }

    // Get sample data for the template
    const sampleData = getSampleData(template)
    
    // Get the email template
    const emailTemplate = emailTemplates[template as keyof typeof emailTemplates]
    if (!emailTemplate) {
      throw new Error(`Template "${template}" not found`)
    }

    const { subject, html } = emailTemplate(sampleData)

    // Get email configuration from Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    const { data: emailConfigData } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'email_config')
      .single()

    if (!emailConfigData?.value) {
      throw new Error('Email configuration not found')
    }

    const emailConfig = emailConfigData.value

    if (!emailConfig.smtpPassword) {
      throw new Error('Email configuration incomplete')
    }

    // Send email using the send-email function
    const response = await fetch(`${supabaseUrl}/functions/v1/send-email`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to,
        subject: `[TEST] ${subject}`,
        htmlContent: html,
        textContent: `This is a test email for template: ${template}`,
        config: emailConfig
      })
    })

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message)
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Test email template "${template}" sent successfully to ${to}` 
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Test email template error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message 
      }),
      { 
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})