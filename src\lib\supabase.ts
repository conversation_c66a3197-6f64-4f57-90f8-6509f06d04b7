import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      settings: {
        Row: {
          id: string
          key: string
          value: any
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          key: string
          value: any
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          key?: string
          value?: any
          description?: string | null
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          name: string
          email: string | null
          phone: string | null
          address: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          email?: string | null
          phone?: string | null
          address?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          email?: string | null
          phone?: string | null
          address?: string | null
          notes?: string | null
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          service_type: string
          duration_months: number
          current_price: number
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          service_type: string
          duration_months: number
          current_price: number
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          service_type?: string
          duration_months?: number
          current_price?: number
          description?: string | null
          updated_at?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          customer_id: string
          product_id: string
          purchase_date: string
          start_date: string
          expiry_date: string
          price_paid: number
          status: string
          buyer_transaction_image: string | null
          seller_transaction_image: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id: string
          product_id: string
          purchase_date: string
          start_date: string
          expiry_date: string
          price_paid: number
          status?: string
          buyer_transaction_image?: string | null
          seller_transaction_image?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string
          product_id?: string
          purchase_date?: string
          start_date?: string
          expiry_date?: string
          price_paid?: number
          status?: string
          buyer_transaction_image?: string | null
          seller_transaction_image?: string | null
          notes?: string | null
          updated_at?: string
        }
      }
      price_history: {
        Row: {
          id: string
          product_id: string
          price: number
          effective_date: string
          created_at: string
        }
        Insert: {
          id?: string
          product_id: string
          price: number
          effective_date: string
          created_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          price?: number
          effective_date?: string
        }
      }
    }
  }
}