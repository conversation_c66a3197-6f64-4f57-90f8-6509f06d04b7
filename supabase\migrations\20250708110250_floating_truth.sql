/*
  # Add settings table for system configuration

  1. New Tables
    - `settings`
      - `id` (uuid, primary key)
      - `key` (text, unique) - Setting identifier
      - `value` (jsonb) - Setting value as JSON
      - `description` (text) - Human readable description
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `settings` table
    - Add policy for authenticated users to manage settings

  3. Initial Data
    - Insert default settings for email, SMS, and notifications
*/

CREATE TABLE IF NOT EXISTS settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  key text UNIQUE NOT NULL,
  value jsonb NOT NULL,
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow full access to settings"
  ON settings
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Create trigger for updated_at
CREATE TRIGGER update_settings_updated_at
  BEFORE UPDATE ON settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert default settings
INSERT INTO settings (key, value, description) VALUES
  ('email_config', '{
    "smtpHost": "",
    "smtpPort": "587",
    "smtpUser": "",
    "smtpPassword": "",
    "fromEmail": "",
    "fromName": "Hệ thống quản lý thuê bao"
  }', 'Email SMTP configuration'),
  
  ('sms_config', '{
    "twilioAccountSid": "",
    "twilioAuthToken": "",
    "twilioPhoneNumber": ""
  }', 'SMS Twilio configuration'),
  
  ('notification_config', '{
    "emailEnabled": true,
    "smsEnabled": false,
    "reminderDays": 3,
    "autoReminder": true
  }', 'Notification preferences')
ON CONFLICT (key) DO NOTHING;