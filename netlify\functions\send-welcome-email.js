const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

// Welcome email template
const welcomeEmailTemplate = (data) => ({
  subject: `🎉 Chào mừng bạn đến với ${data.productName}!`,
  html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng bạn!</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .welcome-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
        .welcome-icon { font-size: 48px; margin-bottom: 15px; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info h3 { margin-top: 0; color: #495057; }
        .highlight { background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Chào mừng bạn!</h1>
            <p>Cảm ơn bạn đã đăng ký dịch vụ của chúng tôi</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="welcome-box">
                <div class="welcome-icon">🎉</div>
                <h3>Đăng ký thành công!</h3>
                <p>Chúc mừng bạn đã đăng ký thành công dịch vụ <strong>${data.productName}</strong>!</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin đăng ký</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Loại dịch vụ:</strong> ${data.serviceType}</p>
                <p><strong>Ngày bắt đầu:</strong> ${data.startDate}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #28a745; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Thời hạn:</strong> ${data.durationMonths} tháng</p>
                <p><strong>Số tiền đã thanh toán:</strong> ${data.pricePaid}</p>
            </div>
            
            <div class="highlight">
                <h4>🔥 Những gì bạn có thể làm ngay bây giờ:</h4>
                <ul>
                    <li>Bắt đầu sử dụng dịch vụ ngay lập tức</li>
                    <li>Truy cập đầy đủ các tính năng premium</li>
                    <li>Nhận hỗ trợ ưu tiên từ đội ngũ của chúng tôi</li>
                </ul>
            </div>
            
            <p>Dịch vụ của bạn sẽ hoạt động cho đến ngày <strong>${data.expiryDate}</strong>. Chúng tôi sẽ gửi thông báo nhắc nhở trước khi dịch vụ hết hạn.</p>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi!</p>
            
            <p>Cảm ơn bạn đã tin tưởng và chọn dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Chúc bạn có trải nghiệm tuyệt vời với dịch vụ!</p>
        </div>
    </div>
</body>
</html>
  `,
  text: `
🎉 CHÀO MỪNG BẠN!

Xin chào ${data.customerName},

Chúc mừng bạn đã đăng ký thành công dịch vụ ${data.productName}!

Thông tin đăng ký:
- Tên dịch vụ: ${data.productName}
- Loại dịch vụ: ${data.serviceType}
- Ngày bắt đầu: ${data.startDate}
- Ngày hết hạn: ${data.expiryDate}
- Thời hạn: ${data.durationMonths} tháng
- Số tiền đã thanh toán: ${data.pricePaid}

Dịch vụ của bạn sẽ hoạt động cho đến ngày ${data.expiryDate}.

Cảm ơn bạn đã tin tưởng và chọn dịch vụ của chúng tôi!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
  `
})

// Send email function
async function sendEmail(to, subject, htmlContent, textContent) {
  try {
    // Get email configuration from settings
    const { data: emailConfigData } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'email_config')
      .single()

    if (!emailConfigData?.value) {
      throw new Error('Email configuration not found')
    }

    const emailConfig = emailConfigData.value

    if (!emailConfig.smtpPassword) {
      throw new Error('Email configuration incomplete')
    }

    const response = await fetch(`${supabaseUrl}/functions/v1/send-email`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to,
        subject,
        htmlContent,
        textContent,
        config: emailConfig
      })
    })

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message)
    }

    return { success: true }
  } catch (error) {
    console.error('Email sending error:', error)
    return { success: false, error: error.message }
  }
}

// Format currency
function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

// Format date
function formatDate(date) {
  return new Date(date).toLocaleDateString('vi-VN')
}

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    }
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed' })
    }
  }

  try {
    const { subscriptionId } = JSON.parse(event.body)

    if (!subscriptionId) {
      throw new Error('Subscription ID is required')
    }

    // Get subscription details with customer and product info
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .select(`
        *,
        customers(name, email, phone),
        products(name, service_type, duration_months, current_price)
      `)
      .eq('id', subscriptionId)
      .single()

    if (error) {
      throw error
    }

    if (!subscription) {
      throw new Error('Subscription not found')
    }

    // Check if customer has email
    if (!subscription.customers.email) {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: false,
          message: 'Customer has no email address'
        })
      }
    }

    // Prepare template data
    const templateData = {
      customerName: subscription.customers.name,
      productName: subscription.products.name,
      serviceType: subscription.products.service_type,
      startDate: formatDate(subscription.start_date),
      expiryDate: formatDate(subscription.expiry_date),
      durationMonths: subscription.products.duration_months,
      pricePaid: formatCurrency(subscription.price_paid)
    }

    const emailTemplate = welcomeEmailTemplate(templateData)
    
    const result = await sendEmail(
      subscription.customers.email,
      emailTemplate.subject,
      emailTemplate.html,
      emailTemplate.text
    )

    if (result.success) {
      console.log(`Welcome email sent to ${subscription.customers.email}`)
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({
          success: true,
          message: 'Welcome email sent successfully'
        })
      }
    } else {
      throw new Error(result.error)
    }

  } catch (error) {
    console.error('Welcome email error:', error)
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    }
  }
}