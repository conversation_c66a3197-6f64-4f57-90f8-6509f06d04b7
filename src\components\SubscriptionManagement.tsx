import React, { useState, useEffect } from 'react'
import { subscriptionService, customerService, productService, type SubscriptionWithRelations, type Customer, type Product } from '../lib/database'
import { notificationService } from '../lib/notifications'
import { formatCurrency, formatDate, getStatusColor, getStatusText, getDaysUntilExpiry, isExpiringSoon } from '../lib/utils'
import { Plus, Edit, Trash2, Upload, Eye, AlertTriangle, Send, Calendar, User, Package, Share2, Copy } from 'lucide-react'
import { useForm } from 'react-hook-form'
import * as yup from 'yup'
import { yupResolver } from '@hookform/resolvers/yup'
import { format, addMonths, differenceInDays } from 'date-fns'

const subscriptionSchema = yup.object({
  customer_id: yup.string().required('Vui lòng chọn khách hàng'),
  product_id: yup.string().required('<PERSON>ui lòng chọn sản phẩm'),
  purchase_date: yup.string().required('<PERSON><PERSON><PERSON> mua là bắt buộc'),
  start_date: yup.string().required('<PERSON><PERSON><PERSON> bắt đầu là bắt buộc'),
  price_paid: yup.number().required('Giá là bắt buộc').positive('Giá phải > 0'),
  status: yup.string().required('Trạng thái là bắt buộc'),
  notes: yup.string().nullable()
})

type SubscriptionFormData = yup.InferType<typeof subscriptionSchema>

export function SubscriptionManagement() {
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithRelations[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingSubscription, setEditingSubscription] = useState<SubscriptionWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageType, setImageType] = useState<'buyer' | 'seller' | null>(null)
  const [buyerImageFile, setBuyerImageFile] = useState<File | null>(null)
  const [sellerImageFile, setSellerImageFile] = useState<File | null>(null)
  const [buyerImagePreview, setBuyerImagePreview] = useState<string | null>(null)
  const [sellerImagePreview, setSellerImagePreview] = useState<string | null>(null)
  const [showNotificationModal, setShowNotificationModal] = useState(false)
  const [selectedSubscriptionForNotification, setSelectedSubscriptionForNotification] = useState<SubscriptionWithRelations | null>(null)
  const [notificationType, setNotificationType] = useState<'reminder' | 'expired' | 'welcome' | 'renewal'>('reminder')
  const [sendingNotification, setSendingNotification] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [selectedSubscriptionForShare, setSelectedSubscriptionForShare] = useState<SubscriptionWithRelations | null>(null)
  const [shareUrl, setShareUrl] = useState('')
  const [copySuccess, setCopySuccess] = useState(false)

  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<SubscriptionFormData>({
    resolver: yupResolver(subscriptionSchema),
    defaultValues: {
      status: 'active'
    }
  })

  const watchedProductId = watch('product_id')
  const watchedStartDate = watch('start_date')

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (watchedProductId) {
      const product = products.find(p => p.id === watchedProductId)
      if (product) {
        setSelectedProduct(product)
        setValue('price_paid', product.current_price)
      }
    }
  }, [watchedProductId, products, setValue])

  useEffect(() => {
    if (watchedStartDate && selectedProduct) {
      const startDate = new Date(watchedStartDate)
      const expiryDate = addMonths(startDate, selectedProduct.duration_months)
      setValue('expiry_date', format(expiryDate, 'yyyy-MM-dd'))
    }
  }, [watchedStartDate, selectedProduct, setValue])

  const fetchData = async () => {
    try {
      const [subscriptionsData, customersData, productsData] = await Promise.all([
        subscriptionService.getAll(),
        customerService.getAll(),
        productService.getAll()
      ])

      setSubscriptions(subscriptionsData)
      setCustomers(customersData.map(c => ({ id: c.id, name: c.name })))
      setProducts(productsData)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: SubscriptionFormData) => {
    try {
      const product = products.find(p => p.id === data.product_id)
      if (!product) return

      const startDate = new Date(data.start_date)
      const expiryDate = addMonths(startDate, product.duration_months)

      const subscriptionData = {
        ...data,
        expiry_date: expiryDate.toISOString().split('T')[0],
        price_paid: Number(data.price_paid)
      }

      let subscriptionId: string
      if (editingSubscription) {
        await subscriptionService.update(editingSubscription.id, subscriptionData)
        subscriptionId = editingSubscription.id
      } else {
        const newSubscription = await subscriptionService.create(subscriptionData)
        subscriptionId = newSubscription.id
      }

      // Upload images if selected
      if (buyerImageFile) {
        await subscriptionService.uploadImage(buyerImageFile, subscriptionId, 'buyer')
      }
      if (sellerImageFile) {
        await subscriptionService.uploadImage(sellerImageFile, subscriptionId, 'seller')
      }

      await fetchData()
      setIsModalOpen(false)
      setEditingSubscription(null)
      setSelectedProduct(null)
      setBuyerImageFile(null)
      setSellerImageFile(null)
      setBuyerImagePreview(null)
      setSellerImagePreview(null)
      reset()
    } catch (error) {
      console.error('Error saving subscription:', error)
      alert('Lỗi khi lưu giao dịch')
    }
  }

  const handleEdit = (subscription: SubscriptionWithRelations) => {
    setEditingSubscription(subscription)
    const product = products.find(p => p.id === subscription.product_id)
    setSelectedProduct(product || null)
    
    reset({
      customer_id: subscription.customer_id,
      product_id: subscription.product_id,
      purchase_date: subscription.purchase_date,
      start_date: subscription.start_date,
      price_paid: subscription.price_paid,
      status: subscription.status,
      notes: subscription.notes
    })
    setIsModalOpen(true)
    
    // Reset image states
    setBuyerImageFile(null)
    setSellerImageFile(null)
    setBuyerImagePreview(subscription.buyer_transaction_image)
    setSellerImagePreview(subscription.seller_transaction_image)
  }

  const handleDelete = async (subscriptionId: string) => {
    if (confirm('Bạn có chắc chắn muốn xóa giao dịch này?')) {
      try {
        await subscriptionService.delete(subscriptionId)
        await fetchData()
      } catch (error) {
        console.error('Error deleting subscription:', error)
      }
    }
  }

  const handleImageFileChange = (file: File | null, type: 'buyer' | 'seller') => {
    if (type === 'buyer') {
      setBuyerImageFile(file)
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => setBuyerImagePreview(e.target?.result as string)
        reader.readAsDataURL(file)
      } else {
        setBuyerImagePreview(null)
      }
    } else {
      setSellerImageFile(file)
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => setSellerImagePreview(e.target?.result as string)
        reader.readAsDataURL(file)
      } else {
        setSellerImagePreview(null)
      }
    }
  }

  const sendReminder = async (subscription: SubscriptionWithRelations) => {
    try {
      await notificationService.sendExpirationReminder(
        subscription.customers.email || '',
        subscription.customers.phone || '',
        subscription.customers.name,
        subscription.products.name,
        formatDate(subscription.expiry_date)
      )
      alert(`Đã gửi nhắc nhở cho khách hàng ${subscription.customers.name}`)
    } catch (error) {
      console.error('Error sending reminder:', error)
      alert('Lỗi khi gửi nhắc nhở')
    }
  }

  const handleShowNotificationModal = (subscription: SubscriptionWithRelations) => {
    setSelectedSubscriptionForNotification(subscription)
    
    // Auto-select appropriate notification type based on subscription status
    const daysUntilExpiry = getDaysUntilExpiry(subscription.expiry_date)
    if (daysUntilExpiry < 0) {
      setNotificationType('expired')
    } else {
      setNotificationType('reminder')
    }
    
    setShowNotificationModal(true)
  }

  const sendManualNotification = async () => {
    if (!selectedSubscriptionForNotification) return
    
    setSendingNotification(true)
    try {
      const subscription = selectedSubscriptionForNotification
      
      switch (notificationType) {
        case 'reminder':
          await notificationService.sendExpirationReminder(
            subscription.customers.email || '',
            subscription.customers.phone || '',
            subscription.customers.name,
            subscription.products.name,
            formatDate(subscription.expiry_date)
          )
          break
        case 'expired':
          await notificationService.sendExpiredNotification(
            subscription.customers.email || '',
            subscription.customers.phone || '',
            subscription.customers.name,
            subscription.products.name,
            formatDate(subscription.expiry_date)
          )
          break
        case 'welcome':
          // Send welcome message using renewal template with welcome SMS
          const welcomeSms = notificationService.getSmsTemplate('welcome', {
            customerName: subscription.customers.name,
            productName: subscription.products.name,
            expiryDate: formatDate(subscription.expiry_date)
          })
          
          if (subscription.customers.phone) {
            await notificationService.sendTestSms(subscription.customers.phone, welcomeSms)
          }
          
          await notificationService.sendRenewalSuccess(
            subscription.customers.email || '',
            subscription.customers.phone || '',
            subscription.customers.name,
            subscription.products.name,
            formatDate(subscription.start_date),
            formatDate(subscription.expiry_date),
            formatCurrency(subscription.price_paid)
          )
          break
        case 'renewal':
          await notificationService.sendRenewalSuccess(
            subscription.customers.email || '',
            subscription.customers.phone || '',
            subscription.customers.name,
            subscription.products.name,
            formatDate(subscription.start_date),
            formatDate(subscription.expiry_date),
            formatCurrency(subscription.price_paid)
          )
          break
      }
      
      alert(`Đã gửi thông báo ${getNotificationTypeText(notificationType)} cho khách hàng ${subscription.customers.name}`)
      setShowNotificationModal(false)
    } catch (error) {
      console.error('Error sending manual notification:', error)
      alert('Lỗi khi gửi thông báo')
    } finally {
      setSendingNotification(false)
    }
  }

  const handleShare = (subscription: SubscriptionWithRelations) => {
    setSelectedSubscriptionForShare(subscription)
    const baseUrl = window.location.origin
    const url = `${baseUrl}/share/${subscription.id}`
    setShareUrl(url)
    setShowShareModal(true)
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = shareUrl
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    }
  }

  const getNotificationTypeText = (type: string) => {
    switch (type) {
      case 'reminder': return 'nhắc nhở sắp hết hạn'
      case 'expired': return 'thông báo đã hết hạn'
      case 'welcome': return 'chào mừng'
      case 'renewal': return 'gia hạn thành công'
      default: return 'thông báo'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Quản lý giao dịch</h2>
        <button
          onClick={() => {
            setEditingSubscription(null)
            setSelectedProduct(null)
            setBuyerImageFile(null)
            setSellerImageFile(null)
            setBuyerImagePreview(null)
            setSellerImagePreview(null)
            reset()
            setIsModalOpen(true)
          }}
          className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-5 w-5" />
          Thêm giao dịch
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="hidden md:block overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Khách hàng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sản phẩm
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ngày
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Giá
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hình ảnh
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {subscriptions.map((subscription) => {
                const daysUntilExpiry = getDaysUntilExpiry(subscription.expiry_date)
                const expiringSoon = isExpiringSoon(subscription.expiry_date)

                return (
                  <tr key={subscription.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {subscription.customers.name}
                          </div>
                          {subscription.customers.email && (
                            <div className="text-sm text-gray-500">
                              {subscription.customers.email}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {subscription.products.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {subscription.products.service_type}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm text-gray-900">
                            {format(new Date(subscription.start_date), 'dd/MM/yyyy')}
                          </div>
                          <div className="text-sm text-gray-500">
                            đến {formatDate(subscription.expiry_date)}
                          </div>
                          {expiringSoon && (
                            <div className="flex items-center gap-1 text-amber-600 text-xs">
                              <AlertTriangle className="h-3 w-3" />
                              Sắp hết hạn ({daysUntilExpiry} ngày)
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(subscription.price_paid)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                        {getStatusText(subscription.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-2">
                        {subscription.buyer_transaction_image && (
                          <button
                            onClick={() => {
                              setImagePreview(subscription.buyer_transaction_image)
                              setImageType('buyer')
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Xem hình chuyển khoản của khách"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                        {subscription.seller_transaction_image && (
                          <button
                            onClick={() => {
                              setImagePreview(subscription.seller_transaction_image)
                              setImageType('seller')
                            }}
                            className="text-green-600 hover:text-green-900"
                            title="Xem hình chuyển khoản của bạn"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {expiringSoon && (
                          <button
                            onClick={() => sendReminder(subscription)}
                            className="text-amber-600 hover:text-amber-900"
                            title="Gửi nhắc nhở"
                          >
                            <Send className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => handleShowNotificationModal(subscription)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Gửi nhắc nhở thủ công"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleShare(subscription)}
                          className="text-green-600 hover:text-green-900"
                          title="Chia sẻ thông tin đơn hàng"
                        >
                          <Share2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEdit(subscription)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(subscription.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden">
          {subscriptions.map((subscription) => {
            const daysUntilExpiry = getDaysUntilExpiry(subscription.expiry_date)
            const expiringSoon = isExpiringSoon(subscription.expiry_date)

            return (
              <div key={subscription.id} className="border-b border-gray-200 p-4">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {subscription.customers.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {subscription.products.name}
                    </p>
                  </div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
                    {getStatusText(subscription.status)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3 text-sm">
                  <div>
                    <span className="text-gray-500">Loại dịch vụ:</span>
                    <p className="font-medium">{subscription.products.service_type}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Giá:</span>
                    <p className="font-medium">{formatCurrency(subscription.price_paid)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Bắt đầu:</span>
                    <p className="font-medium">{format(new Date(subscription.start_date), 'dd/MM/yyyy')}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Hết hạn:</span>
                    <p className="font-medium">{formatDate(subscription.expiry_date)}</p>
                  </div>
                </div>

                {expiringSoon && (
                  <div className="flex items-center gap-1 text-amber-600 text-sm mb-3 bg-amber-50 p-2 rounded">
                    <AlertTriangle className="h-4 w-4" />
                    <span>Sắp hết hạn ({daysUntilExpiry} ngày)</span>
                  </div>
                )}

                {subscription.customers.email && (
                  <div className="text-sm text-gray-600 mb-3">
                    <span className="text-gray-500">Email:</span> {subscription.customers.email}
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleShowNotificationModal(subscription)}
                    className="flex items-center gap-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-md text-sm hover:bg-blue-200"
                  >
                    <Send className="h-4 w-4" />
                    Gửi thông báo
                  </button>
                  
                  <button
                    onClick={() => handleShare(subscription)}
                    className="flex items-center gap-1 px-3 py-2 bg-green-100 text-green-700 rounded-md text-sm hover:bg-green-200"
                  >
                    <Share2 className="h-4 w-4" />
                    Chia sẻ
                  </button>
                  
                  <button
                    onClick={() => handleEdit(subscription)}
                    className="flex items-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200"
                  >
                    <Edit className="h-4 w-4" />
                    Sửa
                  </button>

                  {(subscription.buyer_transaction_image || subscription.seller_transaction_image) && (
                    <div className="flex gap-1">
                      {subscription.buyer_transaction_image && (
                        <button
                          onClick={() => {
                            setImagePreview(subscription.buyer_transaction_image)
                            setImageType('buyer')
                          }}
                          className="flex items-center gap-1 px-3 py-2 bg-green-100 text-green-700 rounded-md text-sm hover:bg-green-200"
                        >
                          <Eye className="h-4 w-4" />
                          Ảnh KH
                        </button>
                      )}
                      {subscription.seller_transaction_image && (
                        <button
                          onClick={() => {
                            setImagePreview(subscription.seller_transaction_image)
                            setImageType('seller')
                          }}
                          className="flex items-center gap-1 px-3 py-2 bg-purple-100 text-purple-700 rounded-md text-sm hover:bg-purple-200"
                        >
                          <Eye className="h-4 w-4" />
                          Ảnh bán
                        </button>
                      )}
                    </div>
                  )}

                  <button
                    onClick={() => handleDelete(subscription.id)}
                    className="flex items-center gap-1 px-3 py-2 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200"
                  >
                    <Trash2 className="h-4 w-4" />
                    Xóa
                  </button>
                </div>
              </div>
            )
          })}
        </div>

        {subscriptions.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">Chưa có giao dịch nào</p>
          </div>
        )}
      </div>

      {/* Share Modal */}
      {showShareModal && selectedSubscriptionForShare && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Share2 className="h-5 w-5 text-green-600" />
              Chia sẻ thông tin đơn hàng
            </h3>
            
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">
                <strong>Khách hàng:</strong> {selectedSubscriptionForShare.customers.name}
              </p>
              <p className="text-sm text-gray-600 mb-2">
                <strong>Sản phẩm:</strong> {selectedSubscriptionForShare.products.name}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Giá:</strong> {formatCurrency(selectedSubscriptionForShare.price_paid)}
              </p>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Đường link chia sẻ:
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={shareUrl}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
                <button
                  onClick={copyToClipboard}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    copySuccess 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {copySuccess ? (
                    <>
                      <span className="flex items-center gap-1">
                        ✓ Đã copy
                      </span>
                    </>
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>
            
            <div className="mb-4 p-3 bg-blue-50 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>Lưu ý:</strong> Link này sẽ hiển thị thông tin đơn hàng công khai. 
                Chỉ chia sẻ với khách hàng hoặc người có liên quan.
              </p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowShareModal(false)
                  setCopySuccess(false)
                }}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Đóng
              </button>
              <a
                href={shareUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 inline-flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                Xem trước
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Notification Type Modal */}
      {showNotificationModal && selectedSubscriptionForNotification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              Gửi thông báo cho {selectedSubscriptionForNotification.customers.name}
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-3">
                Sản phẩm: <strong>{selectedSubscriptionForNotification.products.name}</strong>
              </p>
              <p className="text-sm text-gray-600 mb-4">
                Chọn loại thông báo muốn gửi:
              </p>
              
              <div className="space-y-3">
                <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="notificationType"
                    value="reminder"
                    checked={notificationType === 'reminder'}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium text-gray-900">🔔 Nhắc nhở sắp hết hạn</div>
                    <div className="text-sm text-gray-600">Thông báo dịch vụ sắp hết hạn</div>
                  </div>
                </label>
                
                <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="notificationType"
                    value="expired"
                    checked={notificationType === 'expired'}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium text-gray-900">❌ Thông báo đã hết hạn</div>
                    <div className="text-sm text-gray-600">Thông báo dịch vụ đã hết hạn</div>
                  </div>
                </label>
                
                <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="notificationType"
                    value="welcome"
                    checked={notificationType === 'welcome'}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium text-gray-900">🎉 Email chào mừng</div>
                    <div className="text-sm text-gray-600">Gửi email chào mừng khách hàng mới</div>
                  </div>
                </label>
                
                <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="notificationType"
                    value="renewal"
                    checked={notificationType === 'renewal'}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                    className="text-blue-600"
                  />
                  <div>
                    <div className="font-medium text-gray-900">✅ Thông báo gia hạn thành công</div>
                    <div className="text-sm text-gray-600">Xác nhận gia hạn dịch vụ thành công</div>
                  </div>
                </label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowNotificationModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                disabled={sendingNotification}
              >
                Hủy
              </button>
              <button
                onClick={sendManualNotification}
                disabled={sendingNotification}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {sendingNotification && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
                {sendingNotification ? 'Đang gửi...' : 'Gửi thông báo'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Subscription Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">
              {editingSubscription ? 'Chỉnh sửa giao dịch' : 'Thêm giao dịch mới'}
            </h3>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Khách hàng *
                  </label>
                  <select
                    {...register('customer_id')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn khách hàng</option>
                    {customers.map((customer) => (
                      <option key={customer.id} value={customer.id}>
                        {customer.name}
                      </option>
                    ))}
                  </select>
                  {errors.customer_id && (
                    <p className="text-red-500 text-sm mt-1">{errors.customer_id.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sản phẩm *
                  </label>
                  <select
                    {...register('product_id')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Chọn sản phẩm</option>
                    {products.map((product) => (
                      <option key={product.id} value={product.id}>
                        {product.name} - {product.duration_months} tháng
                      </option>
                    ))}
                  </select>
                  {errors.product_id && (
                    <p className="text-red-500 text-sm mt-1">{errors.product_id.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ngày mua *
                  </label>
                  <input
                    {...register('purchase_date')}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.purchase_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.purchase_date.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Ngày bắt đầu *
                  </label>
                  <input
                    {...register('start_date')}
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.start_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.start_date.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Giá (VND) *
                  </label>
                  <input
                    {...register('price_paid')}
                    type="number"
                    min="0"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {errors.price_paid && (
                    <p className="text-red-500 text-sm mt-1">{errors.price_paid.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Trạng thái *
                  </label>
                  <select
                    {...register('status')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="active">Hoạt động</option>
                    <option value="expired">Hết hạn</option>
                    <option value="cancelled">Đã hủy</option>
                  </select>
                  {errors.status && (
                    <p className="text-red-500 text-sm mt-1">{errors.status.message}</p>
                  )}
                </div>
              </div>

              {/* Image Upload Section */}
              <div className="mt-6 border-t pt-6">
                <h4 className="text-md font-semibold mb-4 text-gray-900">Hình ảnh giao dịch</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Customer Transaction Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hình chuyển khoản của khách hàng
                    </label>
                    <div className="space-y-3">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageFileChange(e.target.files?.[0] || null, 'buyer')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      {buyerImagePreview && (
                        <div className="relative">
                          <img
                            src={buyerImagePreview}
                            alt="Customer transaction"
                            className="w-full h-32 object-cover rounded-md border"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageFileChange(null, 'buyer')}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Seller Transaction Image */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hình chuyển khoản của bạn
                    </label>
                    <div className="space-y-3">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageFileChange(e.target.files?.[0] || null, 'seller')}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      {sellerImagePreview && (
                        <div className="relative">
                          <img
                            src={sellerImagePreview}
                            alt="Seller transaction"
                            className="w-full h-32 object-cover rounded-md border"
                          />
                          <button
                            type="button"
                            onClick={() => handleImageFileChange(null, 'seller')}
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ghi chú
                </label>
                <textarea
                  {...register('notes')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ghi chú về giao dịch..."
                />
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
                >
                  Hủy
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                >
                  {loading && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
                  {editingSubscription ? 'Cập nhật' : 'Thêm'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {imagePreview && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-4 max-w-3xl max-h-[90vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {imageType === 'buyer' ? 'Hình chuyển khoản của khách' : 'Hình chuyển khoản của bạn'}
              </h3>
              <button
                onClick={() => {
                  setImagePreview(null)
                  setImageType(null)
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <img
              src={imagePreview}
              alt="Transaction proof"
              className="max-w-full max-h-[70vh] object-contain"
            />
          </div>
        </div>
      )}
    </div>
  )
}