const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
}

// Email templates
const emailTemplates = {
  expirationReminder: (data) => ({
    subject: `🔔 Nhắc nhở: Dịch vụ ${data.productName} sắp hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nhắc nhở gia hạn dịch vụ</title>
    <style>
        body { font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 24px; font-weight: 600; }
        .content { padding: 30px 20px; }
        .alert-box { background-color: #fef3cd; border: 1px solid #fecaca; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Thông báo quan trọng</h1>
            <p>Dịch vụ của bạn sắp hết hạn</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="alert-box">
                <h3>⚠️ Dịch vụ sắp hết hạn!</h3>
                <p>Chúng tôi muốn nhắc nhở bạn rằng dịch vụ của bạn sẽ hết hạn trong ${data.daysLeft} ngày nữa.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Giá gia hạn:</strong> ${data.price}</p>
            </div>
            
            <p>Để tiếp tục sử dụng dịch vụ mà không bị gián đoạn, vui lòng liên hệ với chúng tôi để gia hạn trước ngày hết hạn.</p>
            
            <p>Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
Xin chào ${data.customerName},

Chúng tôi muốn nhắc nhở bạn rằng dịch vụ ${data.productName} của bạn sẽ hết hạn vào ngày ${data.expiryDate} (còn ${data.daysLeft} ngày).

Để tiếp tục sử dụng dịch vụ mà không bị gián đoạn, vui lòng liên hệ với chúng tôi để gia hạn trước ngày hết hạn.

Cảm ơn bạn đã tin tưởng và sử dụng dịch vụ của chúng tôi!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  expired: (data) => ({
    subject: `❌ Thông báo: Dịch vụ ${data.productName} đã hết hạn`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dịch vụ đã hết hạn</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .expired-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ Dịch vụ đã hết hạn</h1>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="expired-box">
                <h3>⏰ Dịch vụ đã hết hạn!</h3>
                <p>Dịch vụ của bạn đã hết hạn và hiện tại không thể sử dụng được.</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin dịch vụ</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #dc3545; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Giá gia hạn:</strong> ${data.price}</p>
            </div>
            
            <p>Để khôi phục và tiếp tục sử dụng dịch vụ, vui lòng liên hệ với chúng tôi để thực hiện gia hạn.</p>
            
            <p>Chúng tôi rất mong được tiếp tục phục vụ bạn!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
Xin chào ${data.customerName},

Dịch vụ ${data.productName} của bạn đã hết hạn vào ngày ${data.expiryDate}.

Để khôi phục và tiếp tục sử dụng dịch vụ, vui lòng liên hệ với chúng tôi để thực hiện gia hạn.

Chúng tôi rất mong được tiếp tục phục vụ bạn!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  }),

  welcome: (data) => ({
    subject: `🎉 Chào mừng bạn đến với ${data.productName}!`,
    html: `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chào mừng bạn!</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
        .header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px 20px; text-align: center; }
        .content { padding: 30px 20px; }
        .welcome-box { background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; }
        .service-info { background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .footer { background-color: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Chào mừng bạn!</h1>
            <p>Cảm ơn bạn đã đăng ký dịch vụ của chúng tôi</p>
        </div>
        
        <div class="content">
            <p>Xin chào <strong>${data.customerName}</strong>,</p>
            
            <div class="welcome-box">
                <h3>✅ Đăng ký thành công!</h3>
                <p>Chúc mừng bạn đã đăng ký thành công dịch vụ <strong>${data.productName}</strong>!</p>
            </div>
            
            <div class="service-info">
                <h3>📋 Thông tin đăng ký</h3>
                <p><strong>Tên dịch vụ:</strong> ${data.productName}</p>
                <p><strong>Loại dịch vụ:</strong> ${data.serviceType}</p>
                <p><strong>Ngày bắt đầu:</strong> ${data.startDate}</p>
                <p><strong>Ngày hết hạn:</strong> <span style="color: #28a745; font-weight: 600;">${data.expiryDate}</span></p>
                <p><strong>Thời hạn:</strong> ${data.durationMonths} tháng</p>
                <p><strong>Số tiền đã thanh toán:</strong> ${data.pricePaid}</p>
            </div>
            
            <h4>🔥 Những gì bạn có thể làm ngay bây giờ:</h4>
            <ul>
                <li>Bắt đầu sử dụng dịch vụ ngay lập tức</li>
                <li>Truy cập đầy đủ các tính năng premium</li>
                <li>Nhận hỗ trợ ưu tiên từ đội ngũ của chúng tôi</li>
            </ul>
            
            <p>Dịch vụ của bạn sẽ hoạt động cho đến ngày <strong>${data.expiryDate}</strong>. Chúng tôi sẽ gửi thông báo nhắc nhở trước khi dịch vụ hết hạn.</p>
            
            <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi!</p>
            
            <p>Cảm ơn bạn đã tin tưởng và chọn dịch vụ của chúng tôi!</p>
            
            <p>Trân trọng,<br><strong>Đội ngũ hỗ trợ khách hàng</strong></p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống quản lý thuê bao.</p>
            <p>Chúc bạn có trải nghiệm tuyệt vời với dịch vụ!</p>
        </div>
    </div>
</body>
</html>
    `,
    text: `
🎉 CHÀO MỪNG BẠN!

Xin chào ${data.customerName},

Chúc mừng bạn đã đăng ký thành công dịch vụ ${data.productName}!

Thông tin đăng ký:
- Tên dịch vụ: ${data.productName}
- Loại dịch vụ: ${data.serviceType}
- Ngày bắt đầu: ${data.startDate}
- Ngày hết hạn: ${data.expiryDate}
- Thời hạn: ${data.durationMonths} tháng
- Số tiền đã thanh toán: ${data.pricePaid}

Dịch vụ của bạn sẽ hoạt động cho đến ngày ${data.expiryDate}.

Cảm ơn bạn đã tin tưởng và chọn dịch vụ của chúng tôi!

Trân trọng,
Đội ngũ hỗ trợ khách hàng
    `
  })
}

// Send email function
async function sendEmail(to, subject, htmlContent, textContent) {
  try {
    // Get email configuration from settings
    const { data: emailConfigData } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'email_config')
      .single()

    if (!emailConfigData?.value) {
      throw new Error('Email configuration not found')
    }

    const emailConfig = emailConfigData.value

    if (!emailConfig.smtpPassword) {
      throw new Error('Email configuration incomplete')
    }

    const response = await fetch(`${supabaseUrl}/functions/v1/send-email`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to,
        subject,
        htmlContent,
        textContent,
        config: emailConfig
      })
    })

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message)
    }

    return { success: true }
  } catch (error) {
    console.error('Email sending error:', error)
    return { success: false, error: error.message }
  }
}

// Format currency
function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount)
}

// Format date
function formatDate(date) {
  return new Date(date).toLocaleDateString('vi-VN')
}

// Calculate days difference
function getDaysDifference(date1, date2) {
  const diffTime = new Date(date1) - new Date(date2)
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// Main scheduled function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    }
  }

  try {
    console.log('Starting scheduled notification check...')

    // Get notification settings
    const { data: notificationConfigData } = await supabase
      .from('settings')
      .select('value')
      .eq('key', 'notification_config')
      .single()

    const notificationConfig = notificationConfigData?.value || {
      emailEnabled: true,
      reminderDays: 3,
      autoReminder: true
    }

    if (!notificationConfig.autoReminder || !notificationConfig.emailEnabled) {
      console.log('Auto reminders disabled')
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: JSON.stringify({ message: 'Auto reminders disabled' })
      }
    }

    const today = new Date()
    const reminderDate = new Date()
    reminderDate.setDate(today.getDate() + notificationConfig.reminderDays)

    // Get subscriptions that need reminders or are expired
    const { data: subscriptions, error } = await supabase
      .from('subscriptions')
      .select(`
        *,
        customers(name, email, phone),
        products(name, service_type, duration_months, current_price)
      `)
      .eq('status', 'active')

    if (error) {
      throw error
    }

    let remindersSent = 0
    let expiredNotificationsSent = 0

    for (const subscription of subscriptions) {
      const expiryDate = new Date(subscription.expiry_date)
      const daysUntilExpiry = getDaysDifference(expiryDate, today)

      // Skip if customer has no email
      if (!subscription.customers.email) {
        continue
      }

      // Check if subscription is expired
      if (daysUntilExpiry < 0) {
        // Update subscription status to expired
        await supabase
          .from('subscriptions')
          .update({ status: 'expired' })
          .eq('id', subscription.id)

        // Send expired notification
        const templateData = {
          customerName: subscription.customers.name,
          productName: subscription.products.name,
          expiryDate: formatDate(subscription.expiry_date),
          price: formatCurrency(subscription.products.current_price)
        }

        const emailTemplate = emailTemplates.expired(templateData)
        
        const result = await sendEmail(
          subscription.customers.email,
          emailTemplate.subject,
          emailTemplate.html,
          emailTemplate.text
        )

        if (result.success) {
          expiredNotificationsSent++
          console.log(`Expired notification sent to ${subscription.customers.email}`)
        }
      }
      // Check if subscription needs reminder
      else if (daysUntilExpiry <= notificationConfig.reminderDays && daysUntilExpiry > 0) {
        const templateData = {
          customerName: subscription.customers.name,
          productName: subscription.products.name,
          expiryDate: formatDate(subscription.expiry_date),
          daysLeft: daysUntilExpiry,
          price: formatCurrency(subscription.products.current_price)
        }

        const emailTemplate = emailTemplates.expirationReminder(templateData)
        
        const result = await sendEmail(
          subscription.customers.email,
          emailTemplate.subject,
          emailTemplate.html,
          emailTemplate.text
        )

        if (result.success) {
          remindersSent++
          console.log(`Reminder sent to ${subscription.customers.email}`)
        }
      }
    }

    console.log(`Scheduled check completed. Reminders sent: ${remindersSent}, Expired notifications: ${expiredNotificationsSent}`)

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        remindersSent,
        expiredNotificationsSent,
        message: 'Scheduled notifications processed successfully'
      })
    }

  } catch (error) {
    console.error('Scheduled function error:', error)
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    }
  }
}