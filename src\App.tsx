import React, { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { AuthGuard } from './components/AuthGuard'
import { Layout } from './components/Layout'
import { CustomerManagement } from './components/CustomerManagement'
import { ProductManagement } from './components/ProductManagement'
import { SubscriptionManagement } from './components/SubscriptionManagement'
import { Settings } from './components/Settings'
import { SharedSubscription } from './components/SharedSubscription'

function App() {
  console.log('App: Component rendering...')
  const [currentPage, setCurrentPage] = useState('customers')

  const renderCurrentPage = () => {
    try {
      switch (currentPage) {
        case 'customers':
          return <CustomerManagement />
        case 'products':
          return <ProductManagement />
        case 'subscriptions':
          return <SubscriptionManagement />
        case 'settings':
          return <Settings />
        default:
          return <CustomerManagement />
      }
    } catch (error) {
      console.error('Error rendering page:', error)
      return (
        <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <h2 className="font-bold">Error rendering page</h2>
          <p>{error instanceof Error ? error.message : 'Unknown error'}</p>
        </div>
      )
    }
  }

  try {
    return (
      <Router>
        <Routes>
          <Route path="/share/:id" element={<SharedSubscription />} />
          <Route path="*" element={
            <AuthGuard>
              <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
                {renderCurrentPage()}
              </Layout>
            </AuthGuard>
          } />
        </Routes>
      </Router>
    )
  } catch (error) {
    console.error('Error in App component:', error)
    return (
      <div style={{ padding: '20px', backgroundColor: '#fee', minHeight: '100vh' }}>
        <h1 style={{ color: 'red' }}>Application Error</h1>
        <p>Error: {error instanceof Error ? error.message : 'Unknown error'}</p>
        <button onClick={() => window.location.reload()}>Reload Page</button>
      </div>
    )
  }

  // Original code (commented out for debugging)
  /*
  return (
    <Router>
      <Routes>
        <Route path="/share/:id" element={<SharedSubscription />} />
        <Route path="*" element={
          <AuthGuard>
            <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
              {renderCurrentPage()}
            </Layout>
          </AuthGuard>
        } />
      </Routes>
    </Router>
  )
  */
}

export default App