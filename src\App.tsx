import React, { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { AuthGuard } from './components/AuthGuard'
import { Layout } from './components/Layout'
import { CustomerManagement } from './components/CustomerManagement'
import { ProductManagement } from './components/ProductManagement'
import { SubscriptionManagement } from './components/SubscriptionManagement'
import { Settings } from './components/Settings'
import { SharedSubscription } from './components/SharedSubscription'

function App() {
  const [currentPage, setCurrentPage] = useState('customers')

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'customers':
        return <CustomerManagement />
      case 'products':
        return <ProductManagement />
      case 'subscriptions':
        return <SubscriptionManagement />
      case 'settings':
        return <Settings />
      default:
        return <CustomerManagement />
    }
  }

  return (
    <Router>
      <Routes>
        <Route path="/share/:id" element={<SharedSubscription />} />
        <Route path="*" element={
          <AuthGuard>
            <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
              {renderCurrentPage()}
            </Layout>
          </AuthGuard>
        } />
      </Routes>
    </Router>
  )
}

export default App