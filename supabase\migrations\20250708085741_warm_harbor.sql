/*
  # Create subscription management system

  1. New Tables
    - `customers`
      - `id` (uuid, primary key)
      - `name` (text, required)
      - `email` (text, optional)
      - `phone` (text, optional)
      - `address` (text, optional)
      - `notes` (text, optional)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `products`
      - `id` (uuid, primary key)
      - `name` (text, required)
      - `service_type` (text, required)
      - `duration_months` (integer, required)
      - `current_price` (numeric, required)
      - `description` (text, optional)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `subscriptions`
      - `id` (uuid, primary key)
      - `customer_id` (uuid, foreign key)
      - `product_id` (uuid, foreign key)
      - `purchase_date` (date, required)
      - `start_date` (date, required)
      - `expiry_date` (date, required)
      - `price_paid` (numeric, required)
      - `status` (text, default 'active')
      - `buyer_transaction_image` (text, optional)
      - `seller_transaction_image` (text, optional)
      - `notes` (text, optional)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `price_history`
      - `id` (uuid, primary key)
      - `product_id` (uuid, foreign key)
      - `price` (numeric, required)
      - `effective_date` (timestamp, required)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage all data
    - Create storage bucket for transaction images

  3. Changes
    - All tables have proper foreign key constraints
    - Timestamps are automatically managed
    - Price history tracks all price changes
*/

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  email text,
  phone text,
  address text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  service_type text NOT NULL,
  duration_months integer NOT NULL CHECK (duration_months > 0),
  current_price numeric NOT NULL CHECK (current_price > 0),
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  purchase_date date NOT NULL,
  start_date date NOT NULL,
  expiry_date date NOT NULL,
  price_paid numeric NOT NULL CHECK (price_paid > 0),
  status text DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
  buyer_transaction_image text,
  seller_transaction_image text,
  notes text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create price_history table
CREATE TABLE IF NOT EXISTS price_history (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  price numeric NOT NULL CHECK (price > 0),
  effective_date timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_history ENABLE ROW LEVEL SECURITY;

-- Create policies (allow full access to authenticated users)
CREATE POLICY "Allow full access to customers" ON customers
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow full access to products" ON products
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow full access to subscriptions" ON subscriptions
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow full access to price_history" ON price_history
  FOR ALL TO authenticated
  USING (true)
  WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_customer_id ON subscriptions(customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_product_id ON subscriptions(product_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expiry_date ON subscriptions(expiry_date);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_price_history_product_id ON price_history(product_id);
CREATE INDEX IF NOT EXISTS idx_price_history_effective_date ON price_history(effective_date);

-- Create storage bucket for transaction images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('transaction-images', 'transaction-images', true) 
ON CONFLICT (id) DO NOTHING;

-- Create storage policy for transaction images
CREATE POLICY "Allow authenticated users to upload transaction images"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'transaction-images');

CREATE POLICY "Allow authenticated users to view transaction images"
ON storage.objects FOR SELECT TO authenticated
USING (bucket_id = 'transaction-images');

CREATE POLICY "Allow authenticated users to delete transaction images"
ON storage.objects FOR DELETE TO authenticated
USING (bucket_id = 'transaction-images');

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_customers_updated_at
  BEFORE UPDATE ON customers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
  BEFORE UPDATE ON products
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at
  BEFORE UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();